<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('drivers', function (Blueprint $table) {
            // Add new JSON column for exit location IDs
            $table->json('exit_location_ids')->nullable()->after('parking_lot_id');
            
            // Drop the old column
            $table->dropForeign(['parking_lot_id']);
            $table->dropColumn('parking_lot_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('drivers', function (Blueprint $table) {
            // Add back the original column
            $table->foreignId('parking_lot_id')->nullable()->constrained()->onDelete('set null');
            
            // Drop the JSON column
            $table->dropColumn('exit_location_ids');
        });
    }
};
