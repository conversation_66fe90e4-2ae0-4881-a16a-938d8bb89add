<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('parkings', function (Blueprint $table) {
            $table->id();
            $table->string('ticket_number');
            $table->string('plate_number');
            $table->string('mobile_number')->nullable();
            $table->foreignId('brand_id')->nullable()->constrained('brands')->nullOnDelete();
            $table->foreignId('model_id')->nullable()->constrained('models')->nullOnDelete();
            $table->foreignId('color_id')->nullable()->constrained('colors')->nullOnDelete();
            $table->boolean('has_damage')->default(false);
            $table->json('damage_marks')->nullable();
            $table->json('images')->nullable();
            $table->text('notes')->nullable();
            $table->time('parking_time');
            $table->date('parking_date');
            $table->enum('status', ['parked', 'requested', 'dispatched', 'retrieved'])->default('parked');
            $table->enum('payment_status', ['paid', 'unpaid'])->default('unpaid');
            $table->enum('payment_mode', ['Cash', 'Card'])->nullable();
            $table->decimal('amount_received', 10, 2)->default(0);
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('parkings');
    }
};
