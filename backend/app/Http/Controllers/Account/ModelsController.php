<?php

namespace App\Http\Controllers\Account;

use App\Http\Controllers\Controller;
use App\Http\Requests\ModelRequest;
use App\Models\Models;
use Illuminate\Http\Request;

class ModelsController extends Controller
{
    public function index(Request $request)
    {
        $perPage = $request->get('per_page', 10);
        $models = Models::with('brand')->orderBy('id', 'desc')->paginate($perPage);
        return response()->json($models);
    }

    public function store(ModelRequest $request)
    {
        $validatedData = $request->validated();
        $model = Models::create($validatedData);
        return response()->json(['data' => $model, 'message' => 'Model created successfully'], 201);
    }

    public function show($id)
    {
        $model = Models::with('brand')->findOrFail($id);
        return response()->json(['data' => $model]);
    }

    public function update(ModelRequest $request, $id)
    {
        $model = Models::findOrFail($id);
        $validatedData = $request->validated();
        $model->update($validatedData);
        return response()->json(['data' => $model, 'message' => 'Model updated successfully']);
    }

    public function destroy($id)
    {
        $model = Models::findOrFail($id);
        $model->delete();
        return response()->json(['message' => 'Model deleted successfully']);
    }
}
