<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Feedback extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'feedbacks';

    protected $fillable = [
        'ticket_number',
        'customer_name',
        'contact_number',
        'email',
        'feedback_text',        
        'driver_rating',
        'overall_rating',
        'exit_location_id',
        'driver_id'
    ];

    protected $casts = [
        'driver_rating' => 'integer',
        'overall_rating' => 'integer',
    ];    public function exitLocation()
    {
        return $this->belongsTo(ExitLocation::class);
    }

    public function driver()
    {
        return $this->belongsTo(Driver::class);
    }
}