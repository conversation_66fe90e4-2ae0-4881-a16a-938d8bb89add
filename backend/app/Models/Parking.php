<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Parking extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'parkings';     
    protected $fillable = [
        'ticket_number',
        'plate_number',
        'mobile_number',
        'brand_id',
        'model_id',
        'color_id',
        'has_damage',
        'damage_marks',
        'images',
        'notes',
        'parking_time',
        'parking_date',
        'status',
        'payment_status',
        'payment_mode',
        'amount_received',
        'exit_location_id',
        'parking_type',
        'user_id',
        'driver_id',
        'valet_point_id',
        'qr_code'
    ];

    protected $casts = [
        'has_damage' => 'boolean',
        'damage_marks' => 'array',
        'images' => 'array'
    ];

    public function brand()
    {
        return $this->belongsTo(Brand::class);
    }

    public function model()
    {
        return $this->belongsTo(Models::class, 'model_id');
    }   
    
    public function color()
    {
        return $this->belongsTo(Color::class);
    }    
    
    public function exitLocation()
    {
        return $this->belongsTo(ExitLocation::class);
    }

    public function driver()
    {
        return $this->belongsTo(Driver::class);
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function valetPoint()
    {
        return $this->belongsTo(ValetPoint::class);
    }
}