<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Hash;

class Driver extends Model
{
    use HasFactory;    
    protected $fillable = [
        'name',
        'contact_number',
        'exit_location_ids',
        'is_active',
        'user_id',
        'pin'
    ];
    
    protected $casts = [
        'is_active' => 'boolean',
        'exit_location_ids' => 'array'
    ];

    protected $hidden = [
        'pin'
    ];    
    
    public function exitLocations()
    {
        return ExitLocation::whereIn('id', $this->exit_location_ids ?? []);
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }
    
    public function incidents()
    {
        return $this->hasMany(IncidentLog::class);
    }

    public function attendances()
    {
        return $this->hasMany(Attendance::class);
    }

    public function setPin($pin)
    {
        $this->pin = Hash::make($pin);
        return $this;
    }
}