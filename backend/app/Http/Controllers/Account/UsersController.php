<?php

namespace App\Http\Controllers\Account;

use App\Http\Controllers\Controller;
use App\Http\Requests\RegisterRequest;
use App\Http\Resources\Account\UserResource;
use App\Http\Responses\ApiErrorResponse;
use App\Models\Driver;
use App\Models\User;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class UsersController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $perPage = $request->get('per_page', 10);
        $users = User::whereNotIn('id', [1])
            ->orderBy('id', 'desc')
            ->paginate($perPage);

        return UserResource::collection($users)
            ->additional([
                'total' => $users->total(),
                'per_page' => $users->perPage(),
                'current_page' => $users->currentPage(),
                'last_page' => $users->lastPage(),
            ]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(RegisterRequest $request): UserResource
    {
        $data = $request->validated();
        $data['password'] = bcrypt($data['password']);
        $data['user_type'] = 'admin';
        $data['role_id'] = $data['role']; // Store role_id

        if ($request->hasFile('image')) {
            $file = $request->file('image');
            $filename = time() . '_' . $file->getClientOriginalName();
            $destinationPath = public_path('uploads/users');
            $file->move($destinationPath, $filename);
            $imagePath = 'uploads/users/' . $filename;
            $data['image'] = $imagePath;
        }

        $user = User::create($data);
        $role = \App\Models\Role::find($data['role']);
        $user->assignRole($role->name);

        // Create driver record if user has driver role
        if ($data['role'] === 10) {
            $driver = Driver::create([
                'user_id' => $user->id,
                'name' => $user->name,
                'contact_number' => $user->phone ?? null,
                'is_active' => true
            ]);
            
            // Sync exit locations if provided
            if (isset($data['exit_location_ids'])) {
                $driver->exitLocations()->sync($data['exit_location_ids']);
            }
        }

        return new UserResource($user);
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        $user = User::findOrFail($id);
        return new UserResource($user);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        $user = User::findOrFail($id);

        return new UserResource($user);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, $id)
    {
        $validatedData = $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|max:255|unique:users,email,' . $id,
            'role' => 'required',
            'is_active' => 'required|boolean'
        ]);

        $user = User::findOrFail($id);

        if ($request->hasFile('image')) {
            $file = $request->file('image');
            $filename = time() . '_' . $file->getClientOriginalName();
            $destinationPath = public_path('uploads/users');
            $file->move($destinationPath, $filename);
            $imagePath = 'uploads/users/' . $filename;
            $validatedData['image'] = $imagePath;
        }

        // Set role_id before updating
        if (isset($validatedData['role'])) {
            $validatedData['role_id'] = $validatedData['role'];
        }

        $user->update($validatedData);
        
        // Update role if provided
        if (isset($validatedData['role'])) {
            $role = \App\Models\Role::find($validatedData['role']);
            $user->syncRoles([$role->name]);
        }

        // Update associated driver record if exists
        if ($user->role_id === 10) {
            $driver = Driver::where('user_id', $user->id)->first();
            if ($driver) {
                $driver->update([
                    'name' => $user->name,
                    'contact_number' => $user->phone ?? $driver->contact_number,
                    'is_active' => $validatedData['is_active'],
                    'exit_location_ids' => $user->exit_location_ids
                ]);
                
                // // Sync exit locations if provided
                // if (isset($validatedData['exit_location_ids'])) {
                //     $driver->exitLocations()->sync($validatedData['exit_location_ids']);
                // }
            }
        }

        return new UserResource($user);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        $data = User::findOrFail($id);

        if ($data->super_admin) {
            return new ApiErrorResponse('Denied', '', 403);
        }

        // Delete associated driver record if exists
        if ($data->role_id === 10) {
            Driver::where('user_id', $data->id)->delete();
        }

        /*if ($data->image) {
            $imagePath = public_path($data->image);
            if (file_exists($imagePath)) {
                unlink($imagePath);
            }
        }*/

        $data->delete();

        return response()->json(['message' => 'Data deleted successfully']);
    }
}
