<?php

namespace App\Http\Controllers\Account;

use App\Http\Controllers\Controller;
use App\Models\Charge;
use Illuminate\Http\Request;
use App\Http\Requests\ChargeRequest;

class ChargesController extends Controller
{
    public function index()
    {        
        $charges = Charge::with('exitLocation')
            ->orderBy('id', 'desc')
            ->get()
            ->map(function ($charge) {
                $charge->location_name = $charge->exitLocation->name;
                return $charge;
            });
        return response()->json(['data' => $charges]);
    }    
    
    public function store(ChargeRequest $request)
    {
        try {
            // Check for existing charge with same exit_location_id
            $existingCharge = Charge::where('exit_location_id', $request->exit_location_id)->first();
            
            if ($existingCharge) {
                return response()->json([
                    'message' => 'A charge already exists for this Valet Location'
                ], 422);
            }

            $charge = Charge::create($request->validated());
            return response()->json([
                'data' => $charge,
                'message' => 'Service charges created successfully'
            ], 201);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to create service charges',
                'error' => $e->getMessage()
            ], 500);
        }
    }    
    
    public function show($id)
    {
        $charge = Charge::with('exitLocation')->findOrFail($id);
        return response()->json(['data' => $charge]);
    }

    public function update(ChargeRequest $request, $id)
    {
        try {
            $charge = Charge::findOrFail($id);
            
            // Check for existing charge with same exit_location_id, excluding current record
            $existingCharge = Charge::where('exit_location_id', $request->exit_location_id)
                ->where('id', '!=', $id)
                ->first();
            
            if ($existingCharge) {
                return response()->json([
                    'message' => 'A charge already exists for this Valet Location'
                ], 422);
            }

            $charge->update($request->validated());
            return response()->json([
                'data' => $charge,
                'message' => 'Service charges updated successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to update service charges',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    public function destroy($id)
    {
        try {
            $charge = Charge::findOrFail($id);
            $charge->delete();
            return response()->json([
                'message' => 'Service charges deleted successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to delete service charges',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    public function getChargesIdsByParkingType($parking_type)
    {
        try {
            $charges = Charge::where('parking_type', $parking_type)
                ->where('is_active', true)
                ->get();
                
            return response()->json(['data' => $charges]);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to get charges',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}