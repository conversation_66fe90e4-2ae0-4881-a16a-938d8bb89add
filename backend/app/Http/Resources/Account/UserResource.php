<?php

namespace App\Http\Resources\Account;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Facades\DB;

class UserResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $role = DB::table('model_has_roles')
            ->where('model_id', $this->id)
            ->where('model_type', 'App\Models\User')
            ->first();

        return [
            'id' => $this->id,
            'super_admin' => $this->super_admin,
            'name' => $this->name,
            'email' => $this->email,
            'user_type' => $this->user_type,
            'image' => $this->image !== null ? url($this->image) : null,
            'is_active' => $this->is_active,
            'role_id' => $this->role_id,
            'created_at' => $this->created_at->toDateTimeString(),
            'updated_at' => $this->updated_at->toDateTimeString(),
            'roles' => $this->getRoleNames(),
            'permissions' => $this->getAllPermissions()->pluck('name'),
            'exit_location_ids' => $this->exit_location_ids ? $this->exit_location_ids : [],
        ];
    }
}
