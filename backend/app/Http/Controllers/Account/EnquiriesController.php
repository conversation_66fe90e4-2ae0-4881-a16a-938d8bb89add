<?php

namespace App\Http\Controllers\Account;

use App\Http\Controllers\Controller;
use App\Models\Enquiry;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class EnquiriesController extends Controller
{
    /**
     * Display a listing of enquiries.
     */
    public function index(Request $request): JsonResponse
    {
        $query = Enquiry::with('assignedUser');

        if ($request->has('status')) {
            $query->where('status', $request->status);
        }

        if ($request->has('unread')) {
            $query->unread();
        }

        $enquiries = $query->latest()->paginate($request->per_page ?? 15);

        return response()->json($enquiries);
    }

    /**
     * Store a newly created enquiry.
     */
    public function store(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'phone' => 'required|string|max:255',
            'message' => 'required|string',
            'source' => 'sometimes|string|max:255',
        ]);

        $validated['ip_address'] = $request->ip();
        
        $enquiry = Enquiry::create($validated);

        return response()->json($enquiry, 201);
    }

    /**
     * Display the specified enquiry.
     */
    public function show(string $id): JsonResponse
    {
        $enquiry = Enquiry::with('assignedUser')->findOrFail($id);
        $enquiry->markAsRead();
        
        return response()->json($enquiry);
    }

    /**
     * Update the specified enquiry.
     */
    public function update(Request $request, string $id): JsonResponse
    {
        $enquiry = Enquiry::findOrFail($id);

        $validated = $request->validate([
            'status' => 'sometimes|in:new,in_progress,completed,spam',
            'assigned_to' => 'sometimes|nullable|exists:users,id',
            'internal_notes' => 'sometimes|nullable|string',
        ]);

        $enquiry->update($validated);

        return response()->json($enquiry);
    }

    /**
     * Remove the specified enquiry.
     */
    public function destroy(string $id): JsonResponse
    {
        $enquiry = Enquiry::findOrFail($id);
        $enquiry->delete();

        return response()->json(null, 204);
    }
}