<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;

class ReportsExport implements FromCollection, WithHeadings, WithMapping
{
    protected $reports;
    protected $reportType;

    public function __construct($reports, $reportType)
    {
        $this->reports = $reports;
        $this->reportType = $reportType;
    }

    /**
     * Return the collection of reports data
     */
    public function collection()
    {
        return collect($this->reports);
    }

    /**
     * Define custom headers for Excel columns based on report type
     */
    public function headings(): array
    {
        switch ($this->reportType) {
            case 'valet_location':
                return [
                    'Date',
                    'Location Name',
                    'Total Parking Spots',
                    'Filled Parking',
                    'Vacated Parking',
                    'Assigned Drivers',
                    'Total Income (QAR)'
                ];

            case 'income':
            case 'daily_report':
                return [
                    'Date',
                    'Location Name',
                    'Parking Type',
                    'Total Parking Spots',
                    'Filled Parking',
                    'Empty Parking',
                    'Total Income (QAR)'
                ];

            default:
                return [
                    'Date',
                    'Location Name',
                    'Parking Type',
                    'Total Parking Spots',
                    'Filled Parking',
                    'Empty Parking',
                    'Total Income (QAR)'
                ];
        }
    }

    /**
     * Map the values for each row based on report type
     */
    public function map($row): array
    {
        $rowArray = (array) $row;
        
        switch ($this->reportType) {
            case 'valet_location':
                return [
                    isset($rowArray['date']) ? date('Y-m-d', strtotime($rowArray['date'])) : '',
                    $rowArray['location_name'] ?? 'N/A',
                    $rowArray['total_spots'] ?? 0,
                    $rowArray['filled_parking'] ?? 0,
                    $rowArray['vacated_parking'] ?? 0,
                    $rowArray['assigned_drivers'] ?? 0,
                    number_format($rowArray['total_income'] ?? 0, 2)
                ];

            case 'income':
            case 'daily_report':
                return [
                    isset($rowArray['date']) ? date('Y-m-d', strtotime($rowArray['date'])) : '',
                    $rowArray['location_name'] ?? 'N/A',
                    $this->formatParkingType($rowArray['parking_type'] ?? 'N/A'),
                    $rowArray['total_spots'] ?? 0,
                    $rowArray['filled_spots'] ?? 0,
                    $rowArray['empty_spots'] ?? 0,
                    number_format($rowArray['total_income'] ?? 0, 2)
                ];

            default:
                return [
                    isset($rowArray['date']) ? date('Y-m-d', strtotime($rowArray['date'])) : '',
                    $rowArray['location_name'] ?? 'N/A',
                    $this->formatParkingType($rowArray['parking_type'] ?? 'N/A'),
                    $rowArray['total_spots'] ?? 0,
                    $rowArray['filled_spots'] ?? 0,
                    $rowArray['empty_spots'] ?? 0,
                    number_format($rowArray['total_income'] ?? 0, 2)
                ];
        }
    }

    /**
     * Format parking type for better display
     */
    private function formatParkingType($parkingType)
    {
        if (!$parkingType || $parkingType === 'N/A') {
            return 'N/A';
        }
        
        return ucwords(str_replace('_', ' ', strtolower($parkingType)));
    }
}
