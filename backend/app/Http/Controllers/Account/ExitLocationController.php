<?php

namespace App\Http\Controllers\Account;

use App\Http\Controllers\Controller;
use App\Models\ExitLocation;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Storage;

class ExitLocationController extends Controller
{
    public function index(Request $request)
    {
        $perPage = $request->get('per_page', 10);
        $locations = ExitLocation::paginate($perPage);
        return response()->json($locations);
    }    
    
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'parking_space' => 'required|integer|min:0',
            'is_active' => 'required|in:0,1',
            'logo' => 'nullable|image|max:2048', // Allow image upload up to 2MB
            'phone_number' => 'nullable|string|max:20'
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $data = $request->all();        if ($request->hasFile('logo')) {
            $logo = $request->file('logo');
            $filename = time() . '_' . $logo->getClientOriginalName();
            $path = $logo->store('exit-locations', 'public');
            $data['logo'] = $path;
        }

        $location = ExitLocation::create($data);
        return response()->json(['data' => $location], 201);
    }

    public function show(ExitLocation $exitLocation)
    {
        return response()->json(['data' => $exitLocation]);
    }    
    
    public function update(Request $request, ExitLocation $exitLocation)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'parking_space' => 'required|integer|min:0',
            'is_active' => 'required|in:0,1',
            'logo' => 'nullable|image|max:2048', // Allow image upload up to 2MB
            'phone_number' => 'nullable|string|max:20'
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $data = $request->all();        if ($request->hasFile('logo')) {
            // Remove old logo if exists
            if ($exitLocation->logo) {
                Storage::disk('public')->delete($exitLocation->logo);
            }

            $logo = $request->file('logo');
            $filename = time() . '_' . $logo->getClientOriginalName();
            $path = $logo->store('exit-locations', 'public');
            $data['logo'] = $path;
        }

        $exitLocation->update($data);
        return response()->json(['data' => $exitLocation]);
    }    public function destroy(ExitLocation $exitLocation)
    {
        // Delete the logo file if it exists
        if ($exitLocation->logo) {
            Storage::disk('public')->delete($exitLocation->logo);
        }
        
        $exitLocation->delete();
        return response()->json(null, 204);
    }
}