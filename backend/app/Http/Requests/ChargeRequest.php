<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class ChargeRequest extends FormRequest
{
    public function authorize()
    {
        return true;
    }

    public function rules()
    {
        return [
            'parking_type' => 'required|in:VIP,Normal,hotel_parking',
            'rate_type' => 'required|in:flat,hourly',
            'exit_location_id' => 'required|exists:exit_locations,id',
            'rate_per_hour' => 'nullable|numeric|min:0',
            'hourly_rates' => 'nullable|json',
            'grace_period' => 'required|integer|min:0',
            'minimum_fee' => 'required|numeric|min:0',
            'maximum_fee' => 'required|numeric|min:0',
            'effective_from' => 'required|date',
            'is_active' => 'boolean'
        ];
    }
}