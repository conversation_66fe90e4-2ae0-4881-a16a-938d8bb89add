<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('receipt_settings', function (Blueprint $table) {
            $table->string('logo_path')->nullable();
            $table->text('terms_and_conditions_text')->nullable();
            $table->string('company_email_text')->nullable();
            $table->dropForeign(['valet_point_id']);
            $table->dropColumn('valet_point_id');
            $table->foreignId('exit_location_id')->constrained('exit_locations')->onDelete('cascade');
        });
    }

    public function down(): void
    {
        Schema::table('receipt_settings', function (Blueprint $table) {
            $table->dropColumn(['logo_path', 'terms_and_conditions_text', 'company_email_text']);
            $table->dropForeign(['exit_location_id']);
            $table->dropColumn('exit_location_id');
            $table->foreignId('valet_point_id')->constrained('valet_points')->onDelete('cascade');
        });
    }
};
