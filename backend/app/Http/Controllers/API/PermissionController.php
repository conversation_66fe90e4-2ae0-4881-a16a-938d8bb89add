<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\Permission;
use Illuminate\Http\Request;

class PermissionController extends Controller
{
    /**
     * Display a listing of all permissions grouped by category
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $groupedPermissions = Permission::getGroupedPermissions();
        
        return response()->json([
            'status' => 'success',
            'data' => $groupedPermissions
        ]);
    }
}
