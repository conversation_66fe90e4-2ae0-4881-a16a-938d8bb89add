<?php

namespace App\Imports;

use App\Models\ImportTicket;
use Illuminate\Support\Facades\Auth;
use Maatwebsite\Excel\Concerns\ToModel;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\Concerns\WithValidation;
use Maatwebsite\Excel\Concerns\Importable;
use Maatwebsite\Excel\Concerns\SkipsOnError;
use Maatwebsite\Excel\Concerns\SkipsErrors;
use Maatwebsite\Excel\Concerns\WithBatchInserts;
use Maatwebsite\Excel\Validators\Failure;
use Throwable;

class ImportTicketsImport implements ToModel, WithHeadingRow, WithValidation, SkipsOnError, WithBatchInserts
{
    use Importable, SkipsErrors;
    
    /**
     * Handle errors specifically to provide better messages
     */
    public function onError(Throwable $e)
    {
        if (str_contains($e->getMessage(), 'Undefined array key')) {
            return new Failure(
                0, 
                'ticket_number', 
                ["Missing required column. Make sure your file has 'ticket_number' and 'valet_location' columns."],
                []
            );
        }
        
        return new Failure(0, 'general', [$e->getMessage()], []);
    }
    
    /**
     * Batch size for better performance
     */
    public function batchSize(): int
    {
        return 100;
    }
    
    /**
    * @param array $row
    *
    * @return \Illuminate\Database\Eloquent\Model|null
    */
    public function model(array $row)
    {
        // Cast ticket_number to string explicitly to handle numeric values
        return new ImportTicket([
            'ticket_number'  => (string) $row['ticket_number'],
            'valet_location' => $row['valet_location'],
            'status'         => 'active',
            'user_id'        => Auth::id(),
            'imported_at'    => now(),
        ]);
    }
    
    /**
     * @return array
     */
    public function rules(): array
    {
        return [
            'ticket_number'  => 'required',  // Removed string validation to accept both strings and numbers
            'valet_location' => 'required|string',
        ];
    }
}
