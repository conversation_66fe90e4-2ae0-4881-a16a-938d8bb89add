<?php

namespace App\Http\Controllers\Account;

use App\Http\Controllers\Controller;
use App\Models\TicketSetting;
use Illuminate\Http\Request;
use App\Http\Requests\TicketSettingRequest;

class TicketSettingController extends Controller
{
    public function index()
    {
        $settings = TicketSetting::all();
        return response()->json(['data' => $settings]);
    }

    public function store(TicketSettingRequest $request)
    {
        $setting = TicketSetting::create($request->validated());
        return response()->json(['data' => $setting], 201);
    }

    public function show(TicketSetting $ticketSetting)
    {
        return response()->json(['data' => $ticketSetting]);
    }

    public function update(TicketSettingRequest $request, TicketSetting $ticketSetting)
    {
        $ticketSetting->update($request->validated());
        return response()->json(['data' => $ticketSetting]);
    }

    public function destroy(TicketSetting $ticketSetting)
    {
        $ticketSetting->delete();
        return response()->json(null, 204);
    }
}