<!DOCTYPE html>
<html>
<head>
    <title>{{ ucwords(str_replace('_', ' ', $reportType)) }} Report</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            font-size: 12px;
            margin: 0;
            padding: 20px;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #333;
            padding-bottom: 20px;
        }
        .header h1 {
            color: #333;
            margin: 0;
            font-size: 24px;
        }
        .header p {
            margin: 5px 0;
            color: #666;
        }
        .filters {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .filters h3 {
            margin-top: 0;
            color: #333;
        }
        .filter-item {
            display: inline-block;
            margin-right: 20px;
            margin-bottom: 10px;
        }
        .filter-label {
            font-weight: bold;
            color: #555;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 10px 8px;
            text-align: left;
            vertical-align: top;
        }
        th {
            background-color: #f2f2f2;
            font-weight: bold;
            color: #333;
            font-size: 11px;
        }
        td {
            font-size: 10px;
        }
        tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        .text-center {
            text-align: center;
        }
        .text-right {
            text-align: right;
        }
        .no-data {
            text-align: center;
            padding: 40px;
            color: #666;
            font-style: italic;
        }
        .summary {
            margin-top: 20px;
            background-color: #e9ecef;
            padding: 15px;
            border-radius: 5px;
        }
        .summary-item {
            display: inline-block;
            margin-right: 30px;
            font-weight: bold;
        }
        .page-break {
            page-break-after: always;
        }
        .footer {
            margin-top: 30px;
            text-align: center;
            font-size: 10px;
            color: #666;
            border-top: 1px solid #ddd;
            padding-top: 10px;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>{{ ucwords(str_replace('_', ' ', $reportType)) }} Report</h1>
        <p>Generated on: {{ $generatedDate }}</p>
        @if(isset($dateRange))
            <p>Period: {{ $dateRange }}</p>
        @endif
    </div>

    <div class="filters">
        <h3>Report Filters</h3>
        <div class="filter-item">
            <span class="filter-label">Report Type:</span> {{ ucwords(str_replace('_', ' ', $reportType)) }}
        </div>
        @if(isset($filters['parking_lot_name']) && $filters['parking_lot_name'])
            <div class="filter-item">
                <span class="filter-label">Location:</span> {{ $filters['parking_lot_name'] }}
            </div>
        @endif
        @if(isset($filters['driver_name']) && $filters['driver_name'])
            <div class="filter-item">
                <span class="filter-label">Driver:</span> {{ $filters['driver_name'] }}
            </div>
        @endif
        @if(isset($dateRange))
            <div class="filter-item">
                <span class="filter-label">Date Range:</span> {{ $dateRange }}
            </div>
        @endif
    </div>

    @if(count($reports) > 0)
        <table>
            <thead>
                <tr>
                    <th>Date</th>
                    @if($reportType === 'valet_location')
                        <th>Location Name</th>
                        <th>Total Parking Spots</th>
                        <th>Filled Parking</th>
                        <th>Vacated Parking</th>
                        <th>Assigned Drivers</th>
                        <th>Total Income</th>
                    @elseif(in_array($reportType, ['income', 'daily_report']))
                        <th>Location Name</th>
                        <th>Parking Type</th>
                        <th>Total Parking Spots</th>
                        <th>Filled Parking</th>
                        <th>Empty Parking</th>
                        <th>Total Income</th>
                    @endif
                </tr>
            </thead>
            <tbody>
                @foreach($reports as $report)
                    <tr>
                        <td>{{ $report['date'] ? date('M d, Y', strtotime($report['date'])) : 'N/A' }}</td>
                        @if($reportType === 'valet_location')
                            <td>{{ $report['location_name'] ?? 'N/A' }}</td>
                            <td class="text-center">{{ $report['total_spots'] ?? 0 }}</td>
                            <td class="text-center">{{ $report['filled_parking'] ?? 0 }}</td>
                            <td class="text-center">{{ $report['vacated_parking'] ?? 0 }}</td>
                            <td class="text-center">{{ $report['assigned_drivers'] ?? 0 }}</td>
                            <td class="text-right">QAR {{ number_format(floatval($report['total_income'] ?? 0), 2) }}</td>
                        @elseif(in_array($reportType, ['income', 'daily_report']))
                            <td>{{ $report['location_name'] ?? 'N/A' }}</td>
                            <td>{{ ucwords(str_replace('_', ' ', $report['parking_type'] ?? 'N/A')) }}</td>
                            <td class="text-center">{{ $report['total_spots'] ?? 0 }}</td>
                            <td class="text-center">{{ $report['filled_spots'] ?? 0 }}</td>
                            <td class="text-center">{{ $report['empty_spots'] ?? 0 }}</td>
                            <td class="text-right">QAR {{ number_format(floatval($report['total_income'] ?? 0), 2) }}</td>
                        @endif
                    </tr>
                @endforeach
            </tbody>
        </table>

        <div class="summary">
            <h3>Summary</h3>
            <div class="summary-item">Total Records: {{ count($reports) }}</div>
            <div class="summary-item">Total Income: QAR {{ number_format(collect($reports)->sum('total_income'), 2) }}</div>
            @if(in_array($reportType, ['valet_location', 'income', 'daily_report']))
                @php
                    $totalSpots = collect($reports)->sum('total_spots');
                    $totalFilled = $reportType === 'valet_location' 
                        ? collect($reports)->sum('filled_parking') 
                        : collect($reports)->sum('filled_spots');
                @endphp
                @if($totalSpots > 0)
                    <div class="summary-item">Total Parking Spots: {{ $totalSpots }}</div>
                    <div class="summary-item">Total Filled: {{ $totalFilled }}</div>
                    <div class="summary-item">Occupancy Rate: {{ $totalSpots > 0 ? round(($totalFilled / $totalSpots) * 100, 1) : 0 }}%</div>
                @endif
            @endif
        </div>
    @else
        <div class="no-data">
            <h3>No Data Found</h3>
            <p>No reports found for the selected criteria. Please try adjusting your filters.</p>
        </div>
    @endif

    <div class="footer">
        <p>This report was generated automatically by the Valet Parking Management System.</p>
        <p>Generated on {{ $generatedDate }} | Page 1 of 1</p>
    </div>
</body>
</html>
