<?php

namespace App\Http\Controllers\Account;

use App\Http\Controllers\Controller;
use App\Models\ImportTicket;
use App\Imports\ImportTicketsImport;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use Maatwebsite\Excel\Facades\Excel;

class ImportTicketController extends Controller
{
    /**
     * Display a listing of the imported tickets.
     */
    public function index(Request $request)
    {
        $perPage = $request->get('per_page', 10);
        $importedTickets = ImportTicket::orderBy('created_at', 'desc')->paginate($perPage);
        
        return response()->json($importedTickets);
    }

    /**
     * Import tickets from CSV file using Maatwebsite/Excel.
     */
    public function import(Request $request)
    {
        // Validate request
        $validator = Validator::make($request->all(), [
            'csv_file' => 'required|file|mimes:csv,txt,xlsx,xls|max:2048',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        try {
            // Get the file
            $file = $request->file('csv_file');
            
            // Use the import class to process the file
            $import = new ImportTicketsImport();
            
            try {
                Excel::import($import, $file);
                
                // Get any validation errors
                $errors = $import->errors()->toArray();
                
                // Get the imported tickets (we need to query them since Excel::import doesn't return the models)
                $importedTickets = ImportTicket::where('user_id', Auth::id())
                    ->where('imported_at', '>=', now()->subMinutes(5))
                    ->get();
                
                if (count($importedTickets) === 0 && empty($errors)) {
                    // If no records were imported but no errors were raised, likely a header issue
                    return response()->json([
                        'message' => 'No records were imported',
                        'imported_count' => 0,
                        'errors' => ["The file may not have the required columns ('ticket_number' and 'valet_location') or they may be named differently."],
                        'imported_tickets' => []
                    ], 422);
                }
                
                return response()->json([
                    'message' => 'Import completed',
                    'imported_count' => count($importedTickets),
                    'errors' => array_map(function($error) {
                        return is_array($error) ? implode(', ', $error) : $error;
                    }, $errors),
                    'imported_tickets' => $importedTickets
                ]);
                
            } catch (\Maatwebsite\Excel\Validators\ValidationException $e) {
                $failures = $e->failures();
                $errors = [];
                
                foreach ($failures as $failure) {
                    $errors[] = "Row {$failure->row()}: {$failure->errors()[0]}";
                }
                
                return response()->json([
                    'message' => 'Validation failed',
                    'errors' => $errors
                ], 422);
            }
        } catch (\Exception $e) {
            $errorMessage = $e->getMessage();
            
            // Make common error messages more user-friendly
            if (stripos($errorMessage, 'undefined array key') !== false) {
                $errorMessage = "The file is missing required columns. Please make sure your file has 'ticket_number' and 'valet_location' columns.";
            } elseif (stripos($errorMessage, 'couldn\'t read file') !== false) {
                $errorMessage = "Unable to read the file. Please make sure it's a valid CSV or Excel file.";
            }
            
            return response()->json([
                'message' => 'Failed to import file',
                'error' => $errorMessage
            ], 500);
        }
    }

    /**
     * This method was removed as we now store valet locations as strings.
     */

    /**
     * Remove the specified import ticket from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        try {
            $ticket = ImportTicket::findOrFail($id);
            $ticket->delete();
            
            return response()->json([
                'message' => 'Ticket deleted successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to delete ticket',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
