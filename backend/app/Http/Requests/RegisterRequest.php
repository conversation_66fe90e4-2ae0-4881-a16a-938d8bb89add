<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class RegisterRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'name' => ['required', 'string', 'max:255'],
            'email' => ['required', 'string', 'max:255', 'unique:users'],
            'password' => ['required', 'string'],
            'user_type' => ['nullable', 'string'],
            'role' => ['nullable', 'string'],
            'is_active' => 'required|boolean',
            'exit_location_ids' => ['required_if:role,10', 'array'],
            'exit_location_ids.*' => ['exists:exit_locations,id']
        ];
    }
}
