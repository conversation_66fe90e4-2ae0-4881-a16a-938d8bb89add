<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('incident_logs', function (Blueprint $table) {
            $table->id();
            $table->string('car_plate');
            $table->foreignId('driver_id')->constrained('drivers')->onDelete('cascade');
            $table->text('incident_description');
            $table->dateTime('date_time');
            $table->json('images')->nullable(); // Store array of image paths
            $table->boolean('supervisor_approval')->default(false);
            $table->foreignId('user_id')->constrained('users')->onDelete('cascade'); // To track who reported the incident
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('incident_logs');
    }
};
