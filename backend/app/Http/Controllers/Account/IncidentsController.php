<?php

namespace App\Http\Controllers\Account;

use App\Http\Controllers\Controller;
use App\Models\IncidentLog;
use App\Models\Driver;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use App\Http\Requests\IncidentRequest;
use Illuminate\Http\Request;

class IncidentsController extends Controller
{
    public function index(Request $request)
    {
        $perPage = $request->get('per_page', 10); // Default 10 items per page
        
        $user = Auth::user();
        $incidentsQuery = IncidentLog::with(['driver']);
        
        // If user has Driver role, only show incidents related to them
        if ($user && $user->hasRole('Driver')) {
            $driver = Driver::where('user_id', $user->id)->first();
            
            if ($driver) {
                $incidentsQuery->where('driver_id', $driver->id);
            } else {
                return response()->json(['data' => []]);
            }
        }
        
        $incidents = $incidentsQuery
            ->orderBy('created_at', 'desc')
            ->paginate($perPage)
            ->through(function ($incident) {
                return [
                    'id' => $incident->id,
                    'car_plate' => $incident->car_plate,
                    'driver_name' => $incident->driver->name ?? 'N/A',
                    'incident_description' => $incident->incident_description,
                    'date_time' => $incident->date_time,
                    'created_at' => $incident->created_at->format('Y-m-d H:i:s'),
                    'supervisor_approval' => $incident->supervisor_approval,
                    'images' => collect($incident->images)->map(function ($image) {
                        return [
                            'url' => $image,
                            'file_name' => basename($image)
                        ];
                    })
                ];
            });

        return response()->json($incidents);
    }

    public function store(IncidentRequest $request)
    {
        try {            
            $images = [];
            if ($request->hasFile('images')) {
                foreach ($request->file('images') as $image) {
                    $path = $image->store('incidents', 'public');
                    $images[] = $path;
                }
            }

            $incident = IncidentLog::create([
                'car_plate' => $request->car_plate,
                'driver_id' => $request->driver_id, 
                'incident_description' => $request->incident_description,
                'date_time' => $request->date_time,
                'images' => $images,
                'supervisor_approval' => $request->supervisor_approval,
                'user_id' => Auth::id()
            ]);

            return response()->json([
                'message' => 'Incident reported successfully',
                'data' => $incident
            ], 201);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to create incident',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    public function show($id)
    {
        $user = Auth::user();
        $incidentQuery = IncidentLog::with('driver');
        
        // If user has Driver role, ensure they can only access their own incidents
        if ($user && $user->hasRole('Driver')) {
            $driver = Driver::where('user_id', $user->id)->first();
            
            if ($driver) {
                // Verify this incident belongs to the driver
                $incidentQuery->where('driver_id', $driver->id);
            } else {
                return response()->json(['message' => 'Unauthorized access'], 403);
            }
        }
        
        $incident = $incidentQuery->findOrFail($id);
        
        return response()->json([
            'data' => [
                'id' => $incident->id,
                'car_plate' => $incident->car_plate,
                'driver_id' => $incident->driver_id,
                'incident_description' => $incident->incident_description,
                'date_time' => $incident->date_time,
                'supervisor_approval' => $incident->supervisor_approval,
                'images' => collect($incident->images)->map(function ($image) {
                    return [
                        'url' => $image,
                        'file_name' => basename($image)
                    ];
                })
            ]
        ]);
    }

    public function update(IncidentRequest $request, $id)
    {
        $user = Auth::user();
        $incidentQuery = IncidentLog::query();
        
        // If user has Driver role, ensure they can only modify their own incidents
        if ($user && $user->hasRole('Driver')) {
            $driver = Driver::where('user_id', $user->id)->first();
            
            if ($driver) {
                // Verify this incident belongs to the driver
                $incidentQuery->where('driver_id', $driver->id);
            } else {
                return response()->json(['message' => 'Unauthorized access'], 403);
            }
        }
        
        $incident = $incidentQuery->findOrFail($id);
        
        $images = $incident->images ?? [];
        if ($request->hasFile('images')) {
            foreach ($request->file('images') as $image) {
                $path = $image->store('incidents', 'public');
                $images[] = $path;
            }
        }

        $incident->update([
            'car_plate' => $request->car_plate,
            'incident_description' => $request->incident_description,
            'images' => $images,
            'supervisor_approval' => $request->supervisor_approval
        ]);

        return response()->json([
            'message' => 'Incident updated successfully',
            'data' => $incident
        ]);
    }

    public function destroy($id)
    {
        $user = Auth::user();
        $incidentQuery = IncidentLog::query();
        
        // If user has Driver role, ensure they can only delete their own incidents
        if ($user && $user->hasRole('Driver')) {
            $driver = Driver::where('user_id', $user->id)->first();
            
            if ($driver) {
                // Verify this incident belongs to the driver
                $incidentQuery->where('driver_id', $driver->id);
            } else {
                return response()->json(['message' => 'Unauthorized access'], 403);
            }
        }
        
        $incident = $incidentQuery->findOrFail($id);
        
        // Delete associated images
        foreach ($incident->images ?? [] as $image) {
            Storage::delete('public/' . $image);
        }
        
        $incident->delete();
        
        return response()->json(['message' => 'Incident deleted successfully']);
    }

    public function deleteImage($id, $imageIndex)
    {
        $user = Auth::user();
        $incidentQuery = IncidentLog::query();
        
        // If user has Driver role, ensure they can only modify their own incidents
        if ($user && $user->hasRole('Driver')) {
            $driver = Driver::where('user_id', $user->id)->first();
            
            if ($driver) {
                // Verify this incident belongs to the driver
                $incidentQuery->where('driver_id', $driver->id);
            } else {
                return response()->json(['message' => 'Unauthorized access'], 403);
            }
        }
        
        $incident = $incidentQuery->findOrFail($id);
        $images = $incident->images ?? [];
        
        if (isset($images[$imageIndex])) {
            Storage::delete('public/' . $images[$imageIndex]);
            unset($images[$imageIndex]);
            $incident->update(['images' => array_values($images)]);
            return response()->json(['message' => 'Image deleted successfully']);
        }

        return response()->json(['message' => 'Image not found'], 404);
    }
}