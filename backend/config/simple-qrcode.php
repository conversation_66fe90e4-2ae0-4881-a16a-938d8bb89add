<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Simple QrCode Configuration
    |--------------------------------------------------------------------------
    |
    | This file contains configuration options for the Simple QrCode package.
    |
    */

    /*
    |--------------------------------------------------------------------------
    | Image Backend
    |--------------------------------------------------------------------------
    |
    | Specify which image backend to use for generating QR codes.
    | Available options: 'gd', 'imagick'
    |
    */
    'image_backend' => 'gd',

    /*
    |--------------------------------------------------------------------------
    | Default Format
    |--------------------------------------------------------------------------
    |
    | The default format for generated QR codes.
    | Available options: 'png', 'gif', 'jpg', 'svg', 'eps', 'pdf'
    |
    */
    'format' => 'png',

    /*
    |--------------------------------------------------------------------------
    | Default Size
    |--------------------------------------------------------------------------
    |
    | The default size for generated QR codes.
    |
    */
    'size' => 100,

    /*
    |--------------------------------------------------------------------------
    | Default Margin
    |--------------------------------------------------------------------------
    |
    | The default margin around the QR code.
    |
    */
    'margin' => 0,

    /*
    |--------------------------------------------------------------------------
    | Error Correction Level
    |--------------------------------------------------------------------------
    |
    | The error correction level for QR codes.
    | Available options: 'L' (Low), 'M' (Medium), 'Q' (Quartile), 'H' (High)
    |
    */
    'error_correction' => 'M',

    /*
    |--------------------------------------------------------------------------
    | Encoding
    |--------------------------------------------------------------------------
    |
    | The encoding for QR codes.
    |
    */
    'encoding' => 'UTF-8',
];
