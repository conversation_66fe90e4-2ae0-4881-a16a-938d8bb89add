<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Http\Requests\RegisterRequest;
use App\Http\Resources\Account\UserResource;
use App\Http\Responses\ApiErrorResponse;
use App\Models\User;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use App\Models\ExitLocation;
use App\Models\Parking;
use App\Models\ValetPoint;

class HomeController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function getCountries()
    {
        return response()->json(DB::table('countries')->get());

    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(RegisterRequest $request): UserResource
    {
        $data = $request->validated();
        $data['password'] = bcrypt($data['password']);

        if ($request->hasFile('image')) {
            $file = $request->file('image');
            $filename = time() . '_' . $file->getClientOriginalName();
            $destinationPath = public_path('uploads/users');
            $file->move($destinationPath, $filename);
            $imagePath = 'uploads/users/' . $filename;
            $data['image'] = $imagePath;
        }

        $user = User::create($data);
        $user->assignRole($data['role']);

        if ($request->permissions) {
            $user->givePermissionTo($request->permissions);
        }

        return new UserResource($user);
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        $user = User::findOrFail($id);

        return new UserResource($user);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, $id)
    {
        $validatedData = $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|max:255|unique:users,email,' . $id,
        ]);

        $user = User::findOrFail($id);

        if ($request->hasFile('image')) {
            $file = $request->file('image');
            $filename = time() . '_' . $file->getClientOriginalName();
            $destinationPath = public_path('uploads/users');
            $file->move($destinationPath, $filename);
            $imagePath = 'uploads/users/' . $filename;
            $validatedData['image'] = $imagePath;
        }

        $user->update($validatedData);
        $user->syncPermissions($request->permissions);

        return new UserResource($user);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        $data = User::findOrFail($id);

        if ($data->super_admin) {
            return new ApiErrorResponse('Denied', '', 403);
        }

        /*if ($data->image) {
            $imagePath = public_path($data->image);
            if (file_exists($imagePath)) {
                unlink($imagePath);
            }
        }*/

        $data->delete();

        return response()->json(['message' => 'Data deleted successfully']);
    }

    public function getExitLocations()
    {
        $locations = ExitLocation::where('is_active', 1)
            ->select('id', 'name')
            ->get();
        
        return response()->json(['data' => $locations]);
    }

    public function verifyTicket(Request $request)
    {
        $request->validate([
            'ticket_number' => 'required|string'
        ], [
            'ticket_number.required' => 'Ticket number or plate number is required'
        ]);

        $searchValue = $request->ticket_number;
        
        // Search by both ticket number and plate number
        $exists = Parking::where(function($query) use ($searchValue) {
                $query->where('ticket_number', $searchValue)
                      ->orWhere('plate_number', $searchValue);
            })
            ->where('status', '!=', 'retrieved')
            ->exists();
        
        return response()->json([
            'exists' => $exists,
            'message' => $exists ? 'Vehicle found' : 'Invalid ticket or plate number'
        ]);
    }

    public function updatePaymentStatus(Request $request)
    {
        $request->validate([
            'ticket_number' => 'required|string|exists:parkings,ticket_number',
            'payment_mode' => 'required|in:Cash,Card',
            'valet_point_id' => 'required|exists:valet_points,id'
        ]);

        $parking = Parking::where('ticket_number', $request->ticket_number)
            ->where('status', '!=', 'retrieved')
            ->firstOrFail();

        $parking->update([
            'payment_status' => 'paid',
            'payment_mode' => $request->payment_mode,
            'amount_received' => 20, // Fixed amount
            'valet_point_id' => $request->valet_point_id,
            'status' => 'requested'
        ]);

        // Send SMS if mobile number exists
        if ($parking->mobile_number) {
            $this->sendSMS($parking->mobile_number, $parking->ticket_number, 'paid');
        }

        return response()->json([
            'success' => true,
            'message' => 'Payment status updated successfully',
            'data' => [
                'ticket_number' => $parking->ticket_number,
                'status' => $parking->status,
                'payment_status' => $parking->payment_status
            ]
        ]);
    }

    public function getValetPointsByTicketNumber($ticket_number)
    {
        try {
            $parking = Parking::where('ticket_number',$ticket_number)->firstOrFail();
            $query = ValetPoint::where('location_id',$parking->exit_location_id)
                ->where('is_active', true);
            
            $points = $query->orderBy('name')->get();

            return response()->json([
                'data' => $points
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to get Valet Points',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    private function sendSMS($mobile_number, $ticket_number, $status = 'paid')
    {
        if (!$mobile_number) {
            return;
        }

        $text = urlencode("Payment received for ticket #{$ticket_number}. Your car will be brought to the selected exit point. Thank you for using our valet service.");

        $curl = curl_init();
        $options = [
            CURLOPT_TIMEOUT => 30,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_URL => "https://connectsms.vodafone.com.qa/SMSConnect/SendServlet?" . 
                "application=http_gw1609" .
                "&password=3bv934eHb" .
                "&content=$text" .
                "&destination=$mobile_number" .
                "&source=97469" .
                "&mask=ValetPark"
        ];
        
        curl_setopt_array($curl, $options);
        $response = curl_exec($curl);
        curl_close($curl);

        return $response;
    }
}
