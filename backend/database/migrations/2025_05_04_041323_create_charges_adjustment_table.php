<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('charges_adjustment', function (Blueprint $table) {
            $table->id();
            $table->enum('parking_type', ['VIP', 'Normal', 'Complementary']);
            $table->enum('rate_type', ['flat', 'hourly']);
            $table->foreignId('valet_point_id')->constrained('valet_points')->onDelete('cascade');
            $table->decimal('rate_per_hour', 10, 2)->nullable();
            $table->json('hourly_rates')->nullable();
            $table->integer('grace_period');
            $table->decimal('minimum_fee', 10, 2);
            $table->decimal('maximum_fee', 10, 2);
            $table->date('effective_from');
            $table->boolean('is_active')->default(true);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('charges_adjustment');
    }
};
