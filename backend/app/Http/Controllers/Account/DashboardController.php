<?php

namespace App\Http\Controllers\Account;

use App\Http\Controllers\Controller;
use App\Models\Driver;
use App\Models\ExitLocation;
use App\Models\Feedback;
use App\Models\IncidentLog;
use App\Models\Parking;
use App\Models\ValetPoint;
use Carbon\Carbon;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;

class DashboardController extends Controller
{
    public function stats(): JsonResponse
    {
        $today = Carbon::today();
        $yesterday = Carbon::yesterday();
        $weekStart = Carbon::now()->subDays(7);
        $monthStart = Carbon::now()->startOfMonth();

        // Get authenticated user
        $user = Auth::user();
        
        // If user is a driver, return driver-specific stats
        if ($user && $user->driver) {
            return $this->getDriverStats($user->driver, $today, $yesterday, $weekStart, $monthStart);
        }

        // For admin, owner and other roles return all stats
        return response()->json([
            'data' => [
                'total_parkings' => Parking::whereDate('parking_date', $today)->count(),
                'total_spaces' => ExitLocation::where('is_active', true)->sum('parking_space'),
                'free_slots' => ExitLocation::where('is_active', true)->sum('parking_space') - Parking::where('status', 'parked')->count(),
                'assigned_parkings' => Driver::where('is_active', true)
                    ->whereNotNull('exit_location_ids')
                    ->get()
                    ->flatMap(fn($driver) => $driver->exit_location_ids ?? [])
                    ->unique()
                    ->count(),
                'lifetime_parkings' => Parking::count(),
                'total_drivers' => Driver::where('is_active', true)->count(),
                'total_customers' => Parking::whereDate('parking_date', $today)
                    ->distinct('plate_number')
                    ->count('plate_number'),
                'total_incidents' => IncidentLog::whereDate('date_time', $today)->count(),
                'total_feedbacks' => Feedback::whereDate('created_at', $today)->count(),
                'pending_pickups' => Parking::where('status', 'requested')
                    ->whereDate('parking_date', $today)
                    ->count(),
                'damaged_vehicles' => IncidentLog::whereDate('date_time', $today)->count(),
                'lost_tickets' => 0,
                'today_income' => Parking::whereDate('parking_date', $today)
                    ->sum('amount_received'),
                'yesterday_income' => Parking::whereDate('parking_date', $yesterday)
                    ->sum('amount_received'),
                'weekly_income' => Parking::where('parking_date', '>=', $weekStart)
                    ->sum('amount_received'),
                'monthly_income' => Parking::where('parking_date', '>=', $monthStart)
                    ->sum('amount_received')
            ]
        ]);
    }

    private function getDriverStats(Driver $driver, $today, $yesterday, $weekStart, $monthStart): JsonResponse
    {
        return response()->json([
            'data' => [
                'total_parkings' => Parking::where('driver_id', $driver->id)
                    ->whereDate('parking_date', $today)
                    ->count(),
                'assigned_locations' => count($driver->exit_location_ids ?? []),
                'total_customers' => Parking::where('driver_id', $driver->id)
                    ->whereDate('parking_date', $today)
                    ->distinct('plate_number')
                    ->count('plate_number'),
                'total_incidents' => IncidentLog::where('driver_id', $driver->id)
                    ->whereDate('date_time', $today)
                    ->count(),
                'pending_pickups' => Parking::where('driver_id', $driver->id)
                    ->where('status', 'requested')
                    ->whereDate('parking_date', $today)
                    ->count(),
                'damaged_vehicles' => IncidentLog::where('driver_id', $driver->id)
                    ->whereDate('date_time', $today)
                    ->count(),
                'today_income' => Parking::where('driver_id', $driver->id)
                    ->whereDate('parking_date', $today)
                    ->sum('amount_received'),
                'yesterday_income' => Parking::where('driver_id', $driver->id)
                    ->whereDate('parking_date', $yesterday)
                    ->sum('amount_received'),
                'weekly_income' => Parking::where('driver_id', $driver->id)
                    ->where('parking_date', '>=', $weekStart)
                    ->sum('amount_received'),
                'monthly_income' => Parking::where('driver_id', $driver->id)
                    ->where('parking_date', '>=', $monthStart)
                    ->sum('amount_received')
            ]
        ]);
    }
}