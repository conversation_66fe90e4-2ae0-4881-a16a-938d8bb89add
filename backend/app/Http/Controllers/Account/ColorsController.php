<?php

namespace App\Http\Controllers\Account;

use App\Http\Controllers\Controller;
use App\Models\Color;
use Illuminate\Http\Request;

class ColorsController extends Controller
{
    public function index(Request $request)
    {
        $perPage = $request->get('per_page', 10);
        $colors = Color::paginate($perPage);
        return response()->json($colors);
    }

    public function store(Request $request)
    {
        $validatedData = $request->validate([
            'name' => 'required|string|max:255',
            'is_active' => 'required|in:0,1',
        ]);

        $color = Color::create($validatedData);
        return response()->json(['data' => $color, 'message' => 'Color created successfully'], 201);
    }

    public function show($id)
    {
        $color = Color::findOrFail($id);
        return response()->json(['data' => $color]);
    }

    public function update(Request $request, $id)
    {
        $validatedData = $request->validate([
            'name' => 'required|string|max:255',
            'is_active' => 'required|in:0,1',
        ]);

        $color = Color::findOrFail($id);
        $color->update($validatedData);

        return response()->json(['data' => $color, 'message' => 'Color updated successfully']);
    }

    public function destroy($id)
    {
        $color = Color::findOrFail($id);
        $color->delete();
        
        return response()->json(['message' => 'Color deleted successfully']);
    }
}