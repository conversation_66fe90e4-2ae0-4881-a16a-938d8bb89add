<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('parkings', function (Blueprint $table) {
            $table->enum('parking_type', ['VIP', 'Normal', 'Complementary'])->default('Normal')->after('status');
            $table->foreignId('user_id')->nullable()->after('parking_type')->constrained('users')->nullOnDelete();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('parkings', function (Blueprint $table) {
            $table->dropForeign(['user_id']);
            $table->dropColumn(['parking_type', 'user_id']);
        });
    }
};
