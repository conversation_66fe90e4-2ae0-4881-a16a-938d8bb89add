<?php

namespace App\Http\Controllers\Account;

use App\Http\Controllers\Controller;
use App\Exports\ReportsExport;
use Illuminate\Http\Request;
use Maatwebsite\Excel\Facades\Excel;
use Barryvdh\DomPDF\Facade\Pdf;
use App\Models\ExitLocation;
use Illuminate\Support\Facades\Auth;
use App\Models\Driver;
use App\Models\Parking;
use Illuminate\Support\Facades\DB;

class ReportsController extends Controller
{    public function index(Request $request)
    {
        try {
            $query = $this->buildParkingsQuery($request);
            $parkings = $query->get();

            // Get the processed reports data using helper method
            $reports = $this->processReportsData($parkings, $request->report_type);

            return response()->json(['data' => $reports]);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Error fetching reports',
                'error' => $e->getMessage()
            ], 500);
        }
    }
    
    public function getParkingLots()
    {
        try {
            $user = Auth::user();
            $query = ExitLocation::where('is_active', true);
            
            // If user has specific exit_location_ids, filter by them
            if ($user->exit_location_ids && is_array($user->exit_location_ids)) {
                $query->whereIn('id', $user->exit_location_ids);
            }
            $locations = $query->get();
            
            return response()->json(['data' => $locations]);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Error fetching parking lots',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    public function getDrivers()
    {
        try {
            $user = Auth::user();
            $query = Driver::where('is_active', true);
            if ($user->hasRole('Driver')) {
                $query->where('user_id', $user->id);
            }
            $drivers = $query->orderBy('name')->get();
            
            return response()->json(['data' => $drivers]);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Error fetching drivers',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Export reports as Excel file
     */
    public function exportReportsExcel(Request $request)
    {
        try {
            // Get the same data that would be displayed in the frontend
            $query = $this->buildParkingsQuery($request);
            $parkings = $query->get();

            // Get the processed reports data using helper method
            $reports = $this->processReportsData($parkings, $request->report_type);

            // Create filename with report type and date
            $filename = 'reports_' . ($request->report_type ?? 'general') . '_' . date('Y-m-d') . '.xlsx';
            
            return Excel::download(
                new ReportsExport($reports, $request->report_type ?? 'daily_report'), 
                $filename
            );
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Error generating Excel file',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Export reports as PDF file
     */
    public function exportReportsPdf(Request $request)
    {
        try {
            // Get the same data that would be displayed in the frontend
            $query = $this->buildParkingsQuery($request);
            $parkings = $query->get();

            // Get the processed reports data using helper method
            $reports = $this->processReportsData($parkings, $request->report_type);

            // Get additional filter information for display
            $filters = [];
            if ($request->parking_lot_id) {
                $location = ExitLocation::find($request->parking_lot_id);
                $filters['parking_lot_name'] = $location ? $location->name : '';
            }
            if ($request->driver_id) {
                $driver = Driver::find($request->driver_id);
                $filters['driver_name'] = $driver ? $driver->name : '';
            }

            // Prepare data for PDF template
            $data = [
                'reports' => $reports->toArray(),
                'reportType' => $request->report_type ?? 'daily_report',
                'generatedDate' => now()->format('F d, Y \a\t h:i A'),
                'filters' => $filters,
                'dateRange' => $request->start_date && $request->end_date 
                    ? date('M d, Y', strtotime($request->start_date)) . ' - ' . date('M d, Y', strtotime($request->end_date))
                    : null
            ];

            // Create filename with report type and date
            $filename = 'reports_' . ($request->report_type ?? 'general') . '_' . date('Y-m-d') . '.pdf';

            $pdf = Pdf::loadView('pdf.reports', $data);
            $pdf->setPaper('A4', 'landscape'); // Set to landscape for better table display
            
            return $pdf->download($filename);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Error generating PDF file',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Process parkings data into reports based on report type
     */
    private function processReportsData($parkings, $reportType)
    {
        $reports = collect();

        // If no parkings found, return empty collection
        if ($parkings->isEmpty()) {
            return $reports;
        }

        switch ($reportType) {
            case 'valet_location':
                $reports = $parkings->groupBy(['parking_date', 'exit_location_id'])
                    ->map(function($dateGroup) {
                        $firstParking = $dateGroup->first()->first();
                        $location = $firstParking->exitLocation;
                        $totalSpots = $location ? $location->parking_space : 0;
                        $filledParking = $dateGroup->flatten()->where('status', 'parked')->count();
                        
                        return [
                            'date' => $firstParking->parking_date,
                            'location_name' => $location ? $location->name : 'N/A',
                            'total_spots' => $totalSpots,
                            'filled_parking' => $filledParking,
                            'vacated_parking' => max(0, $totalSpots - $filledParking),
                            'assigned_drivers' => $dateGroup->flatten()->pluck('driver_id')->unique()->filter()->count(),
                            'total_income' => $dateGroup->flatten()->sum('amount_received')
                        ];
                    })->values();
                break;

            case 'income':
            case 'daily_report':
                $reports = $parkings->groupBy(['parking_date', 'exit_location_id', 'parking_type'])
                    ->flatMap(function($dateGroup, $date) {
                        return $dateGroup->flatMap(function($locationGroup, $locationId) use ($date) {
                            return $locationGroup->map(function($typeGroup, $parkingType) use ($date, $locationId) {
                                $firstParking = $typeGroup->first();
                                $location = $firstParking->exitLocation;
                                $totalSpots = $location ? $location->parking_space : 0;
                                $filledSpots = $typeGroup->where('status', 'parked')->count();
                                
                                return [
                                    'date' => $date,
                                    'location_name' => $location ? $location->name : 'N/A',
                                    'parking_type' => $parkingType ?? 'N/A',
                                    'total_spots' => $totalSpots,
                                    'filled_spots' => $filledSpots,
                                    'empty_spots' => $totalSpots - $filledSpots,
                                    'total_income' => $typeGroup->sum('amount_received')
                                ];
                            });
                        });
                    })->values();
                break;
        }

        return $reports;
    }

    /**
     * Build the query for parkings data with filters
     */
    private function buildParkingsQuery(Request $request)
    {
        $user = Auth::user();
        $query = Parking::with(['driver', 'exitLocation']);

        // Apply date filters
        if ($request->start_date && $request->end_date) {
            $query->whereBetween('parking_date', [$request->start_date, $request->end_date]);
        }

        // For drivers, always restrict to their assigned exit locations
        if ($user->hasRole('Driver')) {
            $query->where('driver_id', $user->id);
        }

        // Apply location filter if provided
        if ($request->parking_lot_id) {
            $query->where('exit_location_id', $request->parking_lot_id);
        }

        // Apply driver filter if provided
        if ($request->driver_id) {
            $query->where('driver_id', $request->driver_id);
        }

        // For daily report, always filter by current date
        if ($request->report_type === 'daily_report') {
            $query->whereDate('parking_date', now());
        }

        return $query;
    }
}