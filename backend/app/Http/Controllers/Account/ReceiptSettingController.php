<?php

namespace App\Http\Controllers\Account;

use App\Http\Controllers\Controller;
use App\Models\ReceiptSetting;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Storage;

class ReceiptSettingController extends Controller
{
    public function index()
    {
        $settings = ReceiptSetting::all();
        return response()->json(['data' => $settings]);
    }

    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'company_logo' => 'required|boolean',
            'plate_number' => 'required|boolean',
            'parking_datetime' => 'required|boolean',
            'ticket_id' => 'required|boolean',
            'inquiry_number' => 'required|boolean',
            'terms_conditions' => 'required|boolean',
            'terms_and_conditions_text' => 'nullable|string',
            'company_email' => 'required|boolean',
            'company_email_text' => 'nullable|string',
            'custom_text' => 'nullable|string',
            'logo' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'exit_location_id' => 'required|exists:exit_locations,id'
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $data = $request->except('logo');

        if ($request->hasFile('logo')) {
            $path = $request->file('logo')->store('logos', 'public');
            $data['logo_path'] = $path;
        }

        $setting = ReceiptSetting::create($data);
        return response()->json(['data' => $setting], 201);
    }

    public function show(ReceiptSetting $receiptSetting)
    {
        return response()->json(['data' => $receiptSetting]);
    }

    public function update(Request $request, ReceiptSetting $receiptSetting)
    {
        $validator = Validator::make($request->all(), [
            'company_logo' => 'required|boolean',
            'plate_number' => 'required|boolean',
            'parking_datetime' => 'required|boolean',
            'ticket_id' => 'required|boolean',
            'inquiry_number' => 'required|boolean',
            'terms_conditions' => 'required|boolean',
            'terms_and_conditions_text' => 'nullable|string',
            'company_email' => 'required|boolean',
            'company_email_text' => 'nullable|string',
            'custom_text' => 'nullable|string',
            'logo' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'exit_location_id' => 'required|exists:exit_locations,id'
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $data = $request->except('logo');

        if ($request->hasFile('logo')) {
            // Delete old logo if exists
            if ($receiptSetting->logo_path) {
                Storage::disk('public')->delete($receiptSetting->logo_path);
            }

            $path = $request->file('logo')->store('logos', 'public');
            $data['logo_path'] = $path;
        }

        $receiptSetting->update($data);
        return response()->json(['data' => $receiptSetting]);
    }

    public function destroy(ReceiptSetting $receiptSetting)
    {
        // Delete logo file if exists
        if ($receiptSetting->logo_path) {
            Storage::disk('public')->delete($receiptSetting->logo_path);
        }

        $receiptSetting->delete();
        return response()->json(null, 204);
    }
}