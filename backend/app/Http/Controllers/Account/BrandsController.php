<?php
namespace App\Http\Controllers\Account;

use App\Http\Controllers\Controller;
use App\Models\Brand;
use Illuminate\Http\Request;

class BrandsController extends Controller
{
    public function index(Request $request)
    {
        $perPage = $request->get('per_page', 10);
        $brands = Brand::orderBy('created_at', 'desc')->paginate($perPage);
        return response()->json($brands);
    }

    public function store(Request $request)
    {
        $validatedData = $request->validate([
            'name' => 'required|string|max:255',
            'is_active' => 'required|in:0,1',
        ]);

        $brand = Brand::create($validatedData);
        return response()->json(['data' => $brand, 'message' => 'Brand created successfully'], 201);
    }

    public function show($id)
    {
        $brand = Brand::findOrFail($id);
        return response()->json(['data' => $brand]);
    }

    public function update(Request $request, $id)
    {
        $validatedData = $request->validate([
            'name' => 'required|string|max:255',
            'is_active' => 'required|in:0,1',
        ]);

        $brand = Brand::findOrFail($id);
        $brand->update($validatedData);

        return response()->json(['data' => $brand, 'message' => 'Brand updated successfully']);
    }

    public function destroy($id)
    {
        $brand = Brand::findOrFail($id);
        $brand->delete();
        
        return response()->json(['message' => 'Brand deleted successfully']);
    }
}
