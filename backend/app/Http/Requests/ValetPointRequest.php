<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class ValetPointRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'name' => 'required|string|max:255',
            'location_id' => 'required|exists:exit_locations,id',
            'is_active' => 'required|in:0,1'
        ];
    }
}