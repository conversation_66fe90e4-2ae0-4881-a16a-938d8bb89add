<?php

use App\Http\Controllers\Account\BrandsController;
use App\Http\Controllers\Account\BannersController;
use App\Http\Controllers\Account\ModelsController;
use App\Http\Controllers\Account\ColorsController;
use App\Http\Controllers\Account\ExitLocationController;
use App\Http\Controllers\Account\ValetPointController;
use App\Http\Controllers\Account\BroadCastController;
use App\Http\Controllers\Account\PermissionsController;
use App\Http\Controllers\Account\RolesController;
use App\Http\Controllers\Account\UsersController;
use App\Http\Controllers\Account\WebUsersController;
use App\Http\Controllers\API\PermissionController;
use App\Http\Controllers\Auth\MeController;
use App\Http\Controllers\Auth\RegisterController;
use App\Http\Controllers\Auth\SignInController;
use App\Http\Controllers\Auth\SignOutController;
use App\Http\Controllers\HomeController;
use Illuminate\Support\Facades\Broadcast;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Auth\SignUpController;
use App\Http\Controllers\Account\DriversController;
use App\Http\Controllers\Account\ChargesController;
use App\Http\Controllers\Account\IncidentsController;
use App\Http\Controllers\Account\ReceiptSettingController;
use App\Http\Controllers\Account\TicketSettingController;
use App\Http\Controllers\Account\SMSSettingsController;
use App\Http\Controllers\Account\FeedbackController;
use App\Http\Controllers\Account\ParkingController;
use App\Http\Controllers\Account\DashboardController;
use App\Http\Controllers\Account\NotificationsController;
use App\Http\Controllers\ContactController;
use App\Http\Controllers\Account\EnquiriesController;
use App\Http\Controllers\Account\ReportsController;
use App\Http\Controllers\Account\ImportTicketController;
use App\Http\Controllers\Account\AttendanceController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

/*Route::middleware('auth:sanctum')->get('/user', function (Request $request) {
    return $request->user();
});*/


Route::get('countries', [HomeController::class,'getCountries']);
Route::post('contact', [ContactController::class, 'submit']);
Route::get('/exit-locations', [HomeController::class, 'getExitLocations']);
Route::get('parkings/valet-points-ticket/{ticket_number}', [HomeController::class, 'getValetPointsByTicketNumber']);
Route::post('/verify-ticket', [HomeController::class, 'verifyTicket']);
Route::post('/payment-update', [HomeController::class, 'updatePaymentStatus']);
Route::get('/permissions', [PermissionController::class, 'index']);

Broadcast::routes(['middleware' => ['auth:api']]);

Route::prefix('accounts')->group(function () {
    Route::middleware(['auth:api'])->group(function () {
        Route::get('dashboard/stats', [DashboardController::class, 'stats']);
        Route::resource('users', UsersController::class);
        Route::resource('banners', BannersController::class);
        Route::resource('models', ModelsController::class);
        Route::resource('roles', RolesController::class);
        Route::resource('colors', ColorsController::class);
        Route::resource('exit-locations', ExitLocationController::class);
        Route::resource('valet-points', ValetPointController::class);
        Route::resource('permissions', PermissionsController::class);
        Route::resource('brands', BrandsController::class);
        Route::resource('webusers', WebUsersController::class);
        Route::get('drivers/by-location/{exit_location_id}', [DriversController::class, 'getDriversByLocation']);
        Route::resource('drivers', DriversController::class);
        Route::get('drivers/{id}/incidents', [DriversController::class, 'incidents']);
        Route::get('drivers/{id}/history', [DriversController::class, 'history']);
        Route::get('active-exit-locations', [DriversController::class, 'activeExitLocations']);
        Route::resource('incidents', IncidentsController::class);
        Route::delete('incidents/{id}/images/{imageId}', [IncidentsController::class, 'deleteImage']);
        Route::resource('charges', ChargesController::class);
        Route::get('charges/parking-type/{parking_type}', [ChargesController::class, 'getChargesIdsByParkingType']);
        Route::resource('receipt-settings', ReceiptSettingController::class);
        Route::resource('ticket-settings', TicketSettingController::class);
        Route::resource('sms-settings', SMSSettingsController::class);
        Route::post('sms-settings/{id}/test', [SMSSettingsController::class, 'test']);
        Route::resource('feedbacks', FeedbackController::class);
        Route::get('parkings/next-ticket-number', [ParkingController::class, 'getNextTicketNumber']);
        Route::get('parkings/active-exit-locations/{parking_type}', [ParkingController::class, 'getActiveExitLocationsByParkingType']);
        Route::get('parkings/active-valet-points/{parking_id}', [ParkingController::class, 'getActiveValetPoints']);
        Route::get('parkings/{id}/calculate-charges', [ParkingController::class, 'calculateCharges']);
        Route::post('parkings/find-by-plate', [ParkingController::class, 'findByPlateNumber']);
        Route::resource('parkings', ParkingController::class);
        Route::post('parkings/{id}/payment', [ParkingController::class,'updatePaymentStatus']);
        Route::delete('parkings/{id}/images/{imageIndex}', [ParkingController::class, 'deleteImage']);
        Route::put('parkings/{id}/status', [ParkingController::class, 'updateStatus']);
        
        // Attendance routes
        Route::post('attendance/checkin', [AttendanceController::class, 'checkin']);
        Route::post('attendance/checkout', [AttendanceController::class, 'checkout']);
        Route::get('attendance/driver/{driverId}', [AttendanceController::class, 'getDriverStatus']);
        Route::get('attendance/driver/{driverId}/history', [AttendanceController::class, 'getAttendanceHistory']);
        
        Route::get('notifications', [NotificationsController::class, 'index']);
        Route::put('notifications/{id}/read', [NotificationsController::class, 'markAsRead']);
        Route::put('notifications/mark-all-read', [NotificationsController::class, 'markAllAsRead']);
        Route::get('reports', [ReportsController::class, 'index']);
        Route::get('reports/export-excel', [ReportsController::class, 'exportReportsExcel']);
        Route::get('reports/export-pdf', [ReportsController::class, 'exportReportsPdf']);
        Route::get('reports/parking-lots', [ReportsController::class, 'getParkingLots']);
        Route::get('reports/drivers', [ReportsController::class, 'getDrivers']);
        Route::resource('enquiries', EnquiriesController::class);
        
        // Import tickets routes
        Route::get('import-tickets', [ImportTicketController::class, 'index']);
        Route::post('import-tickets/import', [ImportTicketController::class, 'import']);
        Route::delete('import-tickets/{id}', [ImportTicketController::class, 'destroy']);
    });
});

Route::prefix('auth')->group(function (){

    Route::post('signin', [SignInController::class, 'SignIn']);
    Route::post('user-signin', [SignInController::class, 'UserSignIn']);
    Route::post('signup', [SignUpController::class, 'register']);
    Route::post('signupComplete', [SignUpController::class, 'signupComplete']);

    Route::middleware(['auth:api'])->group(function () {
        Route::get('me', [MeController::class, 'index']);
        Route::post('signout', [SignOutController::class, 'SignOut']);
    });
});

Route::get('ips', function (){
    return response()->json([
        'public_ip' => request()->header('X-Forwarded-For'),
        'laravel_ip' => request()->ip(),
        'client_ip' => request()->getClientIp(),
    ]);
});