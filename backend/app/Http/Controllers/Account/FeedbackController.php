<?php

namespace App\Http\Controllers\Account;

use App\Http\Controllers\Controller;
use App\Models\Feedback;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class FeedbackController extends Controller
{
    public function index(Request $request)
    {
        $perPage = $request->get('per_page', 10); // Default 10 items per page
        
        $feedbacks = Feedback::with(['exitLocation', 'driver'])
            ->latest()
            ->paginate($perPage);

        return response()->json($feedbacks);
    }

    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'ticket_number' => 'required|string|max:255',
            'customer_name' => 'required|string|max:255',
            'contact_number' => 'nullable|string|max:255',
            'email' => 'nullable|email|max:255',
            'feedback_text' => 'required|string',
            'driver_rating' => 'required|integer|min:1|max:5',
            'overall_rating' => 'required|integer|min:1|max:5',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'status' => 'error',
                'errors' => $validator->errors()
            ], 422);
        }

        $feedback = Feedback::create($request->all());

        return response()->json([
            'status' => 'success',
            'data' => $feedback
        ], 201);
    }

    public function show($id)
    {
        $feedback = Feedback::with(['exitLocation', 'driver'])->findOrFail($id);

        return response()->json([
            'status' => 'success',
            'data' => $feedback
        ]);
    }

    public function update(Request $request, $id)
    {
        $validator = Validator::make($request->all(), [
            'customer_name' => 'required|string|max:255',
            'contact_number' => 'nullable|string|max:255',
            'email' => 'nullable|email|max:255',
            'feedback_text' => 'required|string',
            'driver_rating' => 'required|integer|min:1|max:5',
            'overall_rating' => 'required|integer|min:1|max:5',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'status' => 'error',
                'errors' => $validator->errors()
            ], 422);
        }

        $feedback = Feedback::findOrFail($id);
        $feedback->update($request->all());

        return response()->json([
            'status' => 'success',
            'data' => $feedback
        ]);
    }

    public function destroy($id)
    {
        $feedback = Feedback::findOrFail($id);
        $feedback->delete();

        return response()->json([
            'status' => 'success',
            'message' => 'Feedback deleted successfully'
        ]);
    }
}