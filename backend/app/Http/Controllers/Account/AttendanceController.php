<?php

namespace App\Http\Controllers\Account;

use App\Http\Controllers\Controller;
use App\Models\Driver;
use App\Models\Attendance;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Carbon\Carbon;

class AttendanceController extends Controller
{
    public function checkin(Request $request)
    {
        $request->validate([
            'driver_id' => 'required|exists:drivers,id',
            'pin' => 'required|string',
        ]);

        $driver = Driver::findOrFail($request->driver_id);

        // Verify PIN
        if (!Hash::check($request->pin, $driver->pin)) {
            return response()->json(['message' => 'Invalid PIN'], 401);
        }

        // Check if driver is already checked in today
        $lastAttendance = $driver->attendances()
            ->whereDate('date', Carbon::today())
            ->latest()
            ->first();

        if ($lastAttendance && $lastAttendance->status === 'check-in') {
            return response()->json(['message' => 'Driver is already checked in'], 400);
        }

        // Create check-in record
        $attendance = Attendance::create([
            'driver_id' => $driver->id,
            'check_in_time' => now()->format('H:i:s'),
            'date' => now(),
            'status' => 'check-in',
        ]);

        return response()->json([
            'message' => 'Check-in successful',
            'attendance' => $attendance
        ]);
    }

    public function checkout(Request $request)
    {
        $request->validate([
            'driver_id' => 'required|exists:drivers,id',
            'pin' => 'required|string',
        ]);

        $driver = Driver::findOrFail($request->driver_id);

        // Verify PIN
        if (!Hash::check($request->pin, $driver->pin)) {
            return response()->json(['message' => 'Invalid PIN'], 401);
        }

        // Check if driver is checked in today
        $lastAttendance = $driver->attendances()
            ->whereDate('date', Carbon::today())
            ->latest()
            ->first();

        if (!$lastAttendance || $lastAttendance->status === 'check-out') {
            return response()->json(['message' => 'Driver is not checked in'], 400);
        }

        // Update attendance record with check-out time
        $lastAttendance->update([
            'check_out_time' => now()->format('H:i:s'),
            'status' => 'check-out'
        ]);

        return response()->json([
            'message' => 'Check-out successful',
            'attendance' => $lastAttendance
        ]);
    }

    public function getDriverStatus(Request $request, $driverId)
    {
        $driver = Driver::findOrFail($driverId);
        $attendance = $driver->attendances()
            ->whereDate('date', Carbon::today())
            ->latest()
            ->first();
        
        return response()->json([
            'status' => $attendance ? $attendance->status : 'check-out',
            'attendance' => $attendance
        ]);
    }

    public function getAttendanceHistory(Request $request, $driverId)
    {
        $request->validate([
            'filter_type' => 'nullable|in:monthly,weekly,custom',
            'start_date' => 'required_if:filter_type,custom|date',
            'end_date' => 'required_if:filter_type,custom|date|after_or_equal:start_date'
        ]);

        $driver = Driver::findOrFail($driverId);
        $query = $driver->attendances();

        switch ($request->filter_type) {
            case 'monthly':
                $query->whereMonth('date', now()->month)
                      ->whereYear('date', now()->year);
                break;
            case 'weekly':
                $query->whereBetween('date', [
                    now()->startOfWeek(),
                    now()->endOfWeek()
                ]);
                break;
            case 'custom':
                $query->whereBetween('date', [
                    Carbon::parse($request->start_date)->startOfDay(),
                    Carbon::parse($request->end_date)->endOfDay()
                ]);
                break;
        }

        $attendances = $query->orderBy('date', 'desc')
                            ->paginate(10);
        
        return response()->json($attendances);
    }
}
