<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('charges_adjustment', function (Blueprint $table) {
            // Drop the foreign key first
            $table->dropForeign(['valet_point_id']);
            $table->dropColumn('valet_point_id');
            
            // Add the new foreign key column
            $table->foreignId('exit_location_id')->constrained('exit_locations')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('charges_adjustment', function (Blueprint $table) {
            // Drop the new foreign key first
            $table->dropForeign(['exit_location_id']);
            $table->dropColumn('exit_location_id');
            
            // Add back the old foreign key column
            $table->foreignId('valet_point_id')->constrained('valet_points')->onDelete('cascade');
        });
    }
};
