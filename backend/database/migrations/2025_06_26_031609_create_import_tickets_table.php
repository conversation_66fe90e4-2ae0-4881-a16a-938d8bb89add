<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('import_tickets', function (Blueprint $table) {
            $table->id();
            $table->string('ticket_number'); // No unique constraint
            $table->string('valet_location'); // Plain string instead of foreign key
            $table->string('status')->default('active'); // active, used, void
            $table->foreignId('user_id')->nullable()->constrained('users');
            $table->timestamp('imported_at')->useCurrent();
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('import_tickets');
    }
};
