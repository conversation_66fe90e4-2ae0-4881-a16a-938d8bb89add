<?php

use App\Models\Notification;

function createNotification($userId, $title, $message, $type = 'info', $data = null)
{
    try {
        return Notification::create([
            'user_id' => $userId,
            'title' => $title,
            'message' => $message,
            'type' => $type,
            'status' => 'unread',
            'data' => $data,
        ]);
    } catch (\Exception $e) {
        \Log::error('Failed to create notification: ' . $e->getMessage());
        \Log::error('Notification data: ', [
            'user_id' => $userId,
            'title' => $title,
            'message' => $message,
            'type' => $type,
            'data' => $data,
        ]);
        throw $e;
    }
}



