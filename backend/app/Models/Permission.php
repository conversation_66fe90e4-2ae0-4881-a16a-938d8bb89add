<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Spatie\Permission\Models\Permission as SpatiePermission;

class Permission extends SpatiePermission
{
    use HasFactory;

    /**
     * Get permissions grouped by category
     *
     * @return \Illuminate\Support\Collection
     */
    public static function getGroupedPermissions()
    {
        $permissions = self::all()->pluck('name');
        $groupedPermissions = collect();

        // Group permissions by their prefix (before the dot)
        foreach ($permissions as $permission) {
            $parts = explode('.', $permission);
            
            if (count($parts) === 2) {
                $group = $parts[0];
                
                if (!$groupedPermissions->has($group)) {
                    $groupedPermissions[$group] = collect();
                }
                
                $groupedPermissions[$group]->push($permission);
            }
        }

        // Format the data for frontend
        $result = $groupedPermissions->map(function ($items, $key) {
            return [
                'parent' => ucfirst($key),
                'children' => $items->toArray()
            ];
        })->values();

        return $result;
    }
}
