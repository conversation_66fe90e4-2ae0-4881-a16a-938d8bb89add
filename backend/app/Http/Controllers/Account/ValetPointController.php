<?php

namespace App\Http\Controllers\Account;

use App\Http\Controllers\Controller;
use App\Http\Requests\ValetPointRequest;
use App\Models\ValetPoint;
use Illuminate\Http\Request;

class ValetPointController extends Controller
{
    public function index(Request $request)
    {
        $perPage = $request->get('per_page', 10);
        $points = ValetPoint::with('location')
            ->orderBy('id', 'desc')
            ->paginate($perPage)
            ->through(function ($point) {
                $point->location_name = $point->location->name;
                return $point;
            });

        return response()->json($points);
    }

    public function store(ValetPointRequest $request)
    {
        $valetPoint = ValetPoint::create($request->validated());
        return response()->json(['data' => $valetPoint, 'message' => 'Valet point created successfully'], 201);
    }

    public function show($id)
    {
        $point = ValetPoint::with('location')->findOrFail($id);
        return response()->json(['data' => $point]);
    }

    public function update(ValetPointRequest $request, $id)
    {
        $point = ValetPoint::findOrFail($id);
        $point->update($request->validated());
        return response()->json(['data' => $point, 'message' => 'Valet point updated successfully']);
    }

    public function destroy($id)
    {
        $point = ValetPoint::findOrFail($id);
        $point->delete();
        return response()->json(['message' => 'Valet point deleted successfully']);
    }
}