<?php

namespace App\Http\Controllers\Account;

use App\Http\Controllers\Controller;
use App\Models\Parking;
use App\Http\Requests\ParkingRequest;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Auth;
use App\Models\ExitLocation;
use App\Models\Charge;
use SimpleSoftwareIO\QrCode\Facades\QrCode;

class ParkingController extends Controller
{
    public function index(Request $request)
    {
        $perPage = $request->get('per_page', 10); // Default 10 items per page
        
        $parkings = Parking::with(['brand', 'model', 'color'])
            ->orderBy('created_at', 'desc')
            ->paginate($perPage);

        // Format the data
        $parkings->getCollection()->transform(function ($parking) {
            if ($parking->qr_code) {
                $parking->qr_code_url = asset('storage/' . $parking->qr_code);
            }
            
            if ($parking->parking_time) {
                $parking->parking_time = date('H:i', strtotime($parking->parking_time));
            }
            if ($parking->parking_date) {
                $parking->parking_date = date('Y-m-d', strtotime($parking->parking_date));
            }
            
            return $parking;
        });

        return response()->json($parkings);
    }

    public function store(ParkingRequest $request)
    {
        $data = $request->except(['images']);
        
        if ($request->hasFile('images')) {
            $images = [];
            foreach ($request->file('images') as $image) {
                $path = $image->store('parking-images', 'public');
                $images[] = $path;
            }
            $data['images'] = $images;
        }

        $data['user_id'] = Auth::id();
        $parking = Parking::create($data);

        $websiteUrl = rtrim(env('WEBSITE_URL', 'https://valet.quikblogs.com'), '/');
        $url = $websiteUrl . '/tracking?ticket=' . $parking->ticket_number;
        
        // Generate QR code with improved settings for better scanning
        $qrCodeSvg = QrCode::format('png')
            ->size(300) // Slightly smaller size for better pixel density
            ->errorCorrection('H') // Highest error correction level
            ->margin(2) // Add margin around the QR code
            ->generate($url);
        
        $fileName = $parking->ticket_number . '.png';
        $path = 'qr_codes/parking/' . $fileName;
        Storage::disk('public')->put($path, $qrCodeSvg);
        $parking->qr_code = $path;
        
        $parking->save();

        if ($request->mobile_number) {
            $this->sendSMS($request->mobile_number, $parking->ticket_number);
        }

        // Create notification for new parking ticket
        createNotification(
            Auth::id(),
            'New Parking Ticket',
            "New parking ticket #{$parking->ticket_number} has been created",
            'info',
            [
                'parking_id' => $parking->id,
                'ticket_number' => $parking->ticket_number,
                'status' => 'created'
            ]
        );

        return response()->json([
            'message' => 'Parking ticket created successfully',
            'data' => $parking
        ], 201);
    }

    public function show($id)
    {
        $parking = Parking::with(['brand', 'model', 'color', 'exitLocation'])->findOrFail($id);
        
        // Build full URL for QR code if it exists
        if ($parking->qr_code) {
            $parking->qr_code_url = asset('storage/' . $parking->qr_code);
        }

        // Format the parking time and date
        if ($parking->parking_time) {
            $parking->parking_time = date('H:i', strtotime($parking->parking_time));
        }
        if ($parking->parking_date) {
            $parking->parking_date = date('Y-m-d', strtotime($parking->parking_date));
        }
        
        return response()->json(['data' => $parking]);
    }    

    public function update(ParkingRequest $request, $id)
    { 
        $parking = Parking::findOrFail($id);
        
        $data = $request->validated();

        if ($request->hasFile('images')) {
            if ($parking->images) {
                foreach ($parking->images as $oldImage) {
                    Storage::disk('public')->delete($oldImage);
                }
            }

            $images = [];
            foreach ($request->file('images') as $image) {
                $path = $image->store('parking-images', 'public');
                $images[] = $path;
            }
            $data['images'] = $images;
        }

        // Update the parking record
        $parking->update($data);

        return response()->json([
            'message' => 'Parking record updated successfully',
            'data' => $parking
        ]);
    }

    public function destroy($id)
    {
        $parking = Parking::findOrFail($id);
        
        // Delete associated images
        if ($parking->images) {
            foreach ($parking->images as $image) {
                Storage::disk('public')->delete($image);
            }
        }

        $parking->delete();

        return response()->json(['message' => 'Parking record deleted successfully']);
    }    
    
    public function updatePaymentStatus(Request $request, $id)
    {
        $request->validate([
            'payment_status' => 'required|in:paid,unpaid',
            'payment_mode' => 'required|in:Cash,Card',
            'amount_received' => 'required|numeric|min:0',
            'valet_point_id' => 'required|exists:valet_points,id'
        ]);

        $parking = Parking::findOrFail($id);
        $parking->update([
            'payment_status' => $request->payment_status,
            'payment_mode' => $request->payment_mode,
            'amount_received' => $request->amount_received,
            'valet_point_id' => $request->valet_point_id,
            'status' => 'requested'
        ]);

        // Send SMS if mobile number exists and payment is marked as paid
        if ($parking->mobile_number && $request->payment_status === 'paid') {
            $this->sendSMS($parking->mobile_number, $parking->ticket_number, 'paid');
        }

        // Create notification for payment
        createNotification(
            Auth::id(),
            'Payment Received',
            "Payment received for ticket #{$parking->ticket_number}",
            'success',
            [
                'parking_id' => $parking->id,
                'ticket_number' => $parking->ticket_number,
                'amount' => $request->amount_received,
                'payment_mode' => $request->payment_mode,
                'status' => 'paid'
            ]
        );

        return response()->json([
            'message' => 'Payment status updated successfully',
            'data' => $parking
        ]);
    }

    public function deleteImage($id, $imageIndex)
    {
        $parking = Parking::findOrFail($id);
        $images = $parking->images ?? [];
        
        if (isset($images[$imageIndex])) {
            Storage::disk('public')->delete($images[$imageIndex]);
            unset($images[$imageIndex]);
            $parking->update(['images' => array_values($images)]);
            return response()->json(['message' => 'Image deleted successfully']);
        }

        return response()->json(['message' => 'Image not found'], 404);
    }

    public function updateStatus(Request $request, $id)
    {
        $request->validate([
            'status' => 'required|in:parked,requested,dispatched,retrieved'
        ]);

        $parking = Parking::findOrFail($id);
        $currentStatus = $parking->status;
        $newStatus = $request->status;

        // Validate status transitions
        $validTransitions = [
            'parked' => ['requested'],
            'requested' => ['dispatched'],
            'dispatched' => ['retrieved'],
            'retrieved' => [] // Final state
        ];

        if (!in_array($newStatus, $validTransitions[$currentStatus] ?? [])) {
            return response()->json([
                'message' => "Invalid status transition from {$currentStatus} to {$newStatus}"
            ], 400);
        }

        $parking->update(['status' => $newStatus]);

        // Send SMS notification if mobile number exists
        if ($parking->mobile_number) {
            $this->sendSMS($parking->mobile_number, $parking->ticket_number, $newStatus);
        }

        return response()->json([
            'message' => 'Status updated successfully',
            'data' => $parking
        ]);
    }   
    
    public function getNextTicketNumber()
    {
        $today = now();
        $datePrefix = $today->format('ymd');
        $keyPrefix = "KEY-{$datePrefix}-";
        
        // Get the last ticket number for today
        $lastTicket = Parking::where('ticket_number', 'like', $keyPrefix . '%')
            ->orderBy('id', 'desc')
            ->first();

        $nextTicketNumber = $lastTicket 
            ? $keyPrefix . str_pad((int) substr($lastTicket->ticket_number, -3) + 1, 3, '0', STR_PAD_LEFT)
            : $keyPrefix . '001';

        return response()->json([
            'ticket_number' => $nextTicketNumber
        ]);
    }    
    
    public function getActiveValetPoints($parking_id)
    {
        try {
            $parking = Parking::findOrFail($parking_id);
            $query = \App\Models\ValetPoint::where('location_id',$parking->exit_location_id)
                ->where('is_active', true);
            
            $points = $query->orderBy('name')->get();

            return response()->json([
                'data' => $points
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to get Valet Points',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    public function getActiveExitLocationsByParkingType($parking_type)
    {
        try {
            $charges = Charge::where('parking_type', $parking_type)
                ->where('is_active', true)->pluck('exit_location_id');
            $user = Auth::user();
            $query = ExitLocation::where('is_active', true);
            
            // If user has specific exit_location_ids, filter by them
            if ($user->exit_location_ids && is_array($user->exit_location_ids)) {
                $query->whereIn('id', $user->exit_location_ids);
            }
            
            $locations = $query->whereIn('id',$charges)->orderBy('name')->get();

            return response()->json([
                'data' => $locations
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to get Valet Locations',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    public function calculateCharges($id)
    {
        try {
            $parking = Parking::findOrFail($id);
            $parkingStart = $parking->created_at;
            $currentTime = now();

            // Get applicable charge
            $charge = Charge::where('parking_type', $parking->parking_type)
                ->where('exit_location_id', $parking->exit_location_id)
                ->where('is_active', true)
                ->where('effective_from', '<=', $currentTime)
                ->orderBy('effective_from', 'desc')
                ->first();

            if (!$charge) {
                return response()->json([
                    'message' => 'No applicable charges found for this parking'
                ], 404);
            }

            // Calculate duration in hours
            $durationInMinutes = $parkingStart->diffInMinutes($currentTime);
            $durationInHours = ceil($durationInMinutes / 60); // Round up to nearest hour

            // Check grace period
            if ($durationInMinutes <= ($charge->grace_period ?? 0)) {
                return response()->json([
                    'amount' => 0,
                    'currency' => 'QAR',
                    'duration' => [
                        'hours' => floor($durationInMinutes / 60),
                        'minutes' => $durationInMinutes % 60
                    ],
                    'rate_type' => $charge->rate_type,
                    'within_grace_period' => true
                ]);
            }

            // Calculate amount based on rate type
            $amount = 0;
            if ($charge->rate_type === 'flat') {
                $amount = $charge->rate_per_hour * $durationInHours;
            } else if ($charge->rate_type === 'hourly' && $charge->hourly_rates) {
                $hourlyRates = json_decode($charge->hourly_rates, true);
                $remainingHours = $durationInHours;
                $currentHour = 1;
                
                while ($remainingHours > 0 && isset($hourlyRates[$currentHour])) {
                    $amount += floatval($hourlyRates[$currentHour]);
                    $remainingHours--;
                    $currentHour++;
                }

                // If there are remaining hours, use the last rate
                if ($remainingHours > 0 && !empty($hourlyRates)) {
                    $lastHour = max(array_keys($hourlyRates));
                    $lastRate = floatval($hourlyRates[$lastHour]);
                    $amount += $remainingHours * $lastRate;
                }
            }

            // Apply minimum and maximum fees if set
            if ($charge->minimum_fee && $amount < $charge->minimum_fee) {
                $amount = $charge->minimum_fee;
            }
            if ($charge->maximum_fee && $amount > $charge->maximum_fee) {
                $amount = $charge->maximum_fee;
            }

            return response()->json([
                'amount' => round($amount, 2),
                'currency' => 'QAR',
                'duration' => [
                    'hours' => floor($durationInMinutes / 60),
                    'minutes' => $durationInMinutes % 60
                ],
                'rate_type' => $charge->rate_type,
                'within_grace_period' => false,
                'charge_details' => [
                    'rate_per_hour' => $charge->rate_per_hour,
                    'minimum_fee' => $charge->minimum_fee,
                    'maximum_fee' => $charge->maximum_fee,
                    'grace_period' => $charge->grace_period
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to calculate charges',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Find a vehicle by plate number
     */
    public function findByPlateNumber(Request $request)
    {
        $request->validate([
            'plate_number' => 'required|string'
        ]);

        $parking = Parking::with(['brand', 'model', 'color'])
            ->where('plate_number', $request->plate_number)
            ->orderBy('created_at', 'desc')
            ->first();

        if (!$parking) {
            return response()->json([
                'message' => 'No previous parking record found with this plate number'
            ], 404);
        }

        // Build full URL for QR code if it exists
        if ($parking->qr_code) {
            $parking->qr_code_url = asset('storage/' . $parking->qr_code);
        }

        return response()->json([
            'data' => $parking
        ]);
    }

    private function sendSMS($mobile_number, $ticket_number, $status = 'created')
    {
        if (!$mobile_number) {
            return;
        }

        // Different message templates based on status
        switch ($status) {
            case 'created':
            case 'parked':
                $text = urlencode("Dear customer, your valet parking ticket number is {$ticket_number}. We will take good care of your vehicle. Show this SMS when collecting your car.");
                break;
                
            case 'requested':
                $text = urlencode("Your vehicle (Ticket #{$ticket_number}) has been requested. We are preparing it for pickup.");
                break;
                
            case 'dispatched':
                $text = urlencode("Your vehicle (Ticket #{$ticket_number}) is ready and being brought to the pickup area.");
                break;
                
            case 'retrieved':
                $text = urlencode("Your vehicle (Ticket #{$ticket_number}) has been successfully retrieved. Thank you for using our valet service.");
                break;
                
            case 'paid':
                $text = urlencode("Payment received for ticket #{$ticket_number}. Thank you for using our valet service. You can collect your vehicle after showing this SMS.");
                break;
        }

        $curl = curl_init();
        $url = "https://connectsms.vodafone.com.qa/SMSConnect/SendServlet?" . 
                "application=http_gw1609" .
                "&password=3bv934eHb" .
                "&content=$text" .
                "&destination=974$mobile_number" .
                "&source=97469" .
                "&mask=ValetPark";
                
        // Log the SMS URL and details
        \Illuminate\Support\Facades\Log::info('Sending SMS', [
            'ticket_number' => $ticket_number,
            'status' => $status,
            'mobile_number' => $mobile_number,
            'url' => $url
        ]);
                
        $options = [
            CURLOPT_TIMEOUT => 30,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_URL => $url
        ];
        
        curl_setopt_array($curl, $options);
        $response = curl_exec($curl);
        
        // Log the SMS response
        \Illuminate\Support\Facades\Log::info('SMS Response', [
            'ticket_number' => $ticket_number,
            'response' => $response
        ]);
        
        curl_close($curl);

        return $response;
    }
}