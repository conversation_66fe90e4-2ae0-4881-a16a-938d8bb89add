<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('receipt_settings', function (Blueprint $table) {
            $table->id();
            $table->boolean('company_logo')->default(false);
            $table->boolean('plate_number')->default(false);
            $table->boolean('parking_datetime')->default(false);
            $table->boolean('ticket_id')->default(false);
            $table->boolean('inquiry_number')->default(false);
            $table->boolean('terms_conditions')->default(false);
            $table->boolean('company_email')->default(false);
            $table->text('custom_text')->nullable();
            $table->foreignId('valet_point_id')->constrained('valet_points')->onDelete('cascade');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('receipt_settings');
    }
};
