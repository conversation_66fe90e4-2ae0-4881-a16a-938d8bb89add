<?php

namespace App\Http\Controllers\Account;

use App\Http\Controllers\Controller;
use App\Models\Driver;
use App\Models\ExitLocation;
use App\Models\User;
use Illuminate\Http\Request;
use App\Http\Requests\DriverRequest;
use Illuminate\Support\Facades\Auth;

class DriversController extends Controller
{
    public function index(Request $request)
    {        
        $user = Auth::user();
        $perPage = $request->get('per_page', 10);
        $driversQuery = Driver::orderBy('id', 'desc');
        
        // If user has Driver role, only show their own record
        if ($user && $user->hasRole('Driver')) {
            $driversQuery->where('user_id', $user->id);
        }
        
        $drivers = $driversQuery->paginate($perPage);
        
        // Add exit location names to each driver
        $drivers->through(function ($driver) {
            $driver->exit_location_names = $driver->exitLocations()->pluck('name')->toArray();
            return $driver;
        });

        return response()->json($drivers);
    }

    public function store(DriverRequest $request)
    {
        try {
            // Create user record first
            $user = User::create([
                'name' => $request->name,
                'email' => $request->email ?? $request->contact_number . '@driver.com', // Use contact number as email if not provided
                'password' => bcrypt('12345678'), // Set a default password
                'user_type' => 'user',
                'is_active' => $request->is_active
            ]);
            
            // Assign driver role (role ID 10)
            $user->assignRole(10);

            // Create driver record with user_id
            $validatedData = $request->validated();
            $validatedData['user_id'] = $user->id;
            
            $driver = Driver::create($validatedData);
            
            // Set PIN if provided
            if ($request->has('pin') && !empty($request->pin)) {
                $driver->setPin($request->pin)->save();
            }

            return response()->json([
                'data' => $driver,
                'message' => 'Driver created successfully'
            ], 201);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to create driver',
                'error' => $e->getMessage()
            ], 500);
        }
    }    
    
    public function show($id)
    {
        $driver = Driver::findOrFail($id);
        $driver->exit_location_names = $driver->exitLocations()->pluck('name')->toArray();
        return response()->json(['data' => $driver]);
    }

    public function update(DriverRequest $request, $id)
    {
        try {
            
            $driver = Driver::findOrFail($id);
            $validatedData = $request->validated();
            
            // Update driver record
            $driver->update($validatedData);
            
            // Update PIN if provided
            if ($request->has('pin') && !empty($request->pin)) {
                $driver->setPin($request->pin)->save();
            }

            // Update associated user record
            if ($driver->user_id) {
                $user = User::find($driver->user_id);
                if ($user) {
                    $user->update([
                        'name' => $driver->name,
                        'is_active' => $driver->is_active,
                        'exit_location_ids' => $driver->exit_location_ids
                    ]);
                }
            }

            return response()->json([
                'data' => $driver,
                'message' => 'Driver updated successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to update driver',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    public function destroy($id)
    {
        try {
            $driver = Driver::findOrFail($id);
            
            // Delete associated user record if exists
            if ($driver->user_id) {
                User::where('id', $driver->user_id)->delete();
            }
            
            $driver->delete();
            return response()->json(['message' => 'Driver deleted successfully']);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to delete driver',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    public function incidents($id)
    {
        $driver = Driver::findOrFail($id);
        $incidents = $driver->incidents()->orderBy('created_at', 'desc')->get()
            ->map(function ($incident) {
                return [
                    'id' => $incident->id,
                    'car_plate' => $incident->car_plate,
                    'driver_id' => $incident->driver_id,
                    'incident_description' => $incident->incident_description,
                    'date_time' => $incident->date_time,
                    'created_at' => $incident->created_at->format('Y-m-d H:i:s'),
                    'supervisor_approval' => $incident->supervisor_approval,
                    'images' => collect($incident->images)->map(function ($image) {
                        return [
                            'url' => $image,
                            'file_name' => basename($image)
                        ];
                    })
                ];
            });
        return response()->json(['data' => $incidents]);
    }

    public function history($id)
    {
        $driver = Driver::findOrFail($id);
        $history = $driver->parkingHistory()->orderBy('created_at', 'desc')->get();
        return response()->json(['data' => $history]);
    }
    
    public function activeExitLocations()
    {
        $exitLocations = ExitLocation::where('is_active', true)
            ->orderBy('name')
            ->get();
            
        return response()->json(['data' => $exitLocations]);
    }

    public function getDriversByLocation($exit_location_id)
    {
        try {
            $user = Auth::user();
            $drivers = Driver::where('is_active', true)
                ->where(function($query) use ($exit_location_id) {
                    $query->whereJsonContains('exit_location_ids', $exit_location_id)
                          ->orWhereJsonContains('exit_location_ids', (int)$exit_location_id);
                });

            if ($user->hasRole('Driver')) {
                $drivers->where('user_id', $user->id);
            }

            $drivers = $drivers->orderBy('name')
                ->get();

            // Add exit location names to each driver
            $drivers->each(function ($driver) {
                $driver->exit_location_names = $driver->exitLocations()->pluck('name')->toArray();
            });

            return response()->json(['data' => $drivers]);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to fetch drivers',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}