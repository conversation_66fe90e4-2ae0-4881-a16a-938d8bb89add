<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class ParkingRequest extends FormRequest
{
    public function authorize()
    {
        return true;
    }

    public function rules()
    {
        $rules = [
            'plate_number' => 'required|string',
            'mobile_number' => 'nullable|string|regex:/^\d{8}$/',  // Changed to handle empty string
            'brand_id' => 'nullable|exists:brands,id',
            'model_id' => 'nullable|exists:models,id',
            'color_id' => 'nullable|exists:colors,id',
            'has_damage' => 'boolean',
            'damage_marks' => 'nullable|json',
            'images.*' => 'nullable|image|mimes:jpeg,png|max:2048',
            'notes' => 'nullable|string',
            'parking_type' => 'required|in:VIP,Normal,Complementary',
            'status' => 'nullable|in:parked,requested,dispatched,retrieved',
            'driver_id' => 'nullable|exists:drivers,id',
            'user_id' => 'nullable|exists:users,id',
            'parking_time' => 'nullable|date_format:H:i:s',
            'parking_date' => 'nullable|date'
        ];

        if ($this->isMethod('POST')) {
            $rules['ticket_number'] = 'required|string|unique:parkings,ticket_number';
            $rules['parking_time'] = 'required|date_format:H:i:s';
            $rules['parking_date'] = 'required|date';
        }

        return $rules;
    }    
      
    protected function prepareForValidation()
    {
        $input = $this->request->all();

        // For PUT requests, get the existing record
        if ($this->isMethod('PUT')) {
            $id = $this->route('id');
            if ($id) {
                $parking = \App\Models\Parking::find($id);
                if ($parking) {
                    // Keep existing values for missing fields
                    $input = array_merge([
                        'plate_number' => $parking->plate_number,
                        'mobile_number' => $parking->mobile_number ?: null, // Fix the OR operator
                        'brand_id' => $parking->brand_id,
                        'model_id' => $parking->model_id,
                        'color_id' => $parking->color_id,
                        'parking_type' => $parking->parking_type,
                        'status' => $parking->status,
                        'driver_id' => $parking->driver_id,
                        'user_id' => $parking->user_id
                    ], array_filter($input, function ($value) {
                        return $value !== null && $value !== '';
                    }));
                }
            }
        }

        $this->merge($input);
    }
}