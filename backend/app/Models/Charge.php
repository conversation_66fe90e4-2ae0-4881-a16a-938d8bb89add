<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Charge extends Model
{
    use HasFactory;

    protected $table = 'charges_adjustment';

    protected $fillable = [
        'parking_type',
        'rate_type',
        'exit_location_id',
        'rate_per_hour',
        'hourly_rates',
        'grace_period',
        'minimum_fee',
        'maximum_fee',
        'effective_from',
        'is_active'
    ];

    protected $casts = [
        'rate_per_hour' => 'decimal:2',
        'minimum_fee' => 'decimal:2',
        'maximum_fee' => 'decimal:2',
        'hourly_rates' => 'json',
        'effective_from' => 'date',
        'is_active' => 'boolean'
    ];    
    
    public function exitLocation()
    {
        return $this->belongsTo(ExitLocation::class);
    }
}