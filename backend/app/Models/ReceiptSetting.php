<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ReceiptSetting extends Model
{
    use HasFactory;    
    protected $fillable = [
        'company_logo',        
        'plate_number',
        'parking_datetime',
        'ticket_id',
        'inquiry_number',
        'terms_conditions',
        'terms_and_conditions_text',
        'company_email',
        'company_email_text',
        'custom_text',
        'logo_path',
        'exit_location_id'
    ];

    protected $casts = [
        'company_logo' => 'boolean',
        'plate_number' => 'boolean',        
        'parking_datetime' => 'boolean',
        'ticket_id' => 'boolean',
        'inquiry_number' => 'boolean',
        'terms_conditions' => 'boolean',
        'company_email' => 'boolean',
    ];    
    
    public function exitLocation()
    {
        return $this->belongsTo(ExitLocation::class);
    }
}