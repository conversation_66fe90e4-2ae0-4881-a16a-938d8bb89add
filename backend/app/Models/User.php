<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Lara<PERSON>\Passport\HasApiTokens;
use Spatie\Permission\Traits\HasRoles;


class User extends Authenticatable
{
    use HasApiTokens, HasFactory, Notifiable, HasRoles;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'first_name',
        'last_name',
        'name',
        'email',
        'password',
        'user_type',
        'is_active',
        'image',
        'nationality',
        'dob',
        'referral_source',
        'sales_agent_id',
        'residence_country',
        'how_hear_about_us',
        'exit_location_ids',
        'role_id'
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'email_verified_at' => 'datetime',
        'password' => 'hashed',
        'exit_location_ids' => 'array'
    ];

    public function setNameAttribute($value)
    {
        $this->attributes['name'] = ucwords($value);
    }

    public function getNameAttribute($value)
    {
        return ucwords($value);

    }

    public function scopeUserType($query, $type = 'user')
    {
        return $query->where('user_type', $type);
    }

    /*public function comments(): HasMany
    {
        return $this->hasMany(Comment::class);

    }

    public function feedbacks(): HasMany
    {
        return $this->hasMany(FeedBack::class);

    }*/

    public function notifications()
    {
        return $this->hasMany(Notification::class);
    }

    public function exitLocations()
    {
        return ExitLocation::whereIn('id', $this->exit_location_ids ?? []);
    }

    public function getRoleData()
    {
        $role = $this->roles()->first();
        return [
            'id' => $role ? $role->id : null,
            'name' => $role ? $role->name : null
        ];
    }

    public function getRoleName()
    {
        $role = $this->roles()->first();
        return $role ? $role->name : null;
    }

    public function getRoleId()
    {
        $role = $this->roles()->first();
        return $role ? $role->id : null;
    }
}
