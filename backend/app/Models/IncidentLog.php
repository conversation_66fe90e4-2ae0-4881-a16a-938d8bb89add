<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class IncidentLog extends Model
{
    use HasFactory;

    protected $fillable = [
        'car_plate',
        'driver_id',
        'incident_description',
        'date_time',
        'images',
        'supervisor_approval',
        'user_id'
    ];

    protected $casts = [
        'images' => 'array',
        'supervisor_approval' => 'boolean',
        'date_time' => 'datetime'
    ];

    public function driver()
    {
        return $this->belongsTo(Driver::class);
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }
}