<?php

namespace App\Http\Controllers\Account;

use App\Http\Controllers\Controller;
use App\Models\SMSSetting;
use Illuminate\Http\Request;
use App\Http\Requests\SMSSettingRequest;

class SMSSettingsController extends Controller
{
    public function index()
    {
        $settings = SMSSetting::orderBy('id', 'desc')->get();
        return response()->json(['data' => $settings]);
    }

    public function store(SMSSettingRequest $request)
    {
        try {
            $setting = SMSSetting::create($request->validated());
            return response()->json([
                'data' => $setting,
                'message' => 'SMS setting created successfully'
            ], 201);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to create SMS setting',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    public function show($id)
    {
        $setting = SMSSetting::findOrFail($id);
        return response()->json(['data' => $setting]);
    }

    public function update(SMSSettingRequest $request, $id)
    {
        try {
            $setting = SMSSetting::findOrFail($id);
            $setting->update($request->validated());
            return response()->json([
                'data' => $setting,
                'message' => 'SMS setting updated successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to update SMS setting',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    public function destroy($id)
    {
        try {
            $setting = SMSSetting::findOrFail($id);
            $setting->delete();
            return response()->json([
                'message' => 'SMS setting deleted successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to delete SMS setting',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    public function test($id)
    {
        try {
            $setting = SMSSetting::findOrFail($id);
            // Implement SMS testing logic here
            return response()->json([
                'message' => 'Test SMS sent successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to send test SMS',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}