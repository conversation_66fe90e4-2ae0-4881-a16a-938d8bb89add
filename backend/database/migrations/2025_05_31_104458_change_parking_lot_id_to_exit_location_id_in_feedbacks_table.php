<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('feedbacks', function (Blueprint $table) {
            // Drop the foreign key constraint first
            $table->dropForeign(['parking_lot_id']);
            
            // Rename the column
            $table->renameColumn('parking_lot_id', 'exit_location_id');
            
            // Add the new foreign key constraint
            $table->foreign('exit_location_id')
                ->references('id')
                ->on('exit_locations')
                ->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('feedbacks', function (Blueprint $table) {
            // Drop the foreign key constraint first
            $table->dropForeign(['exit_location_id']);
            
            // Rename the column back
            $table->renameColumn('exit_location_id', 'parking_lot_id');
            
            // Add back the original foreign key constraint
            $table->foreign('parking_lot_id')
                ->references('id')
                ->on('parking_lots')
                ->onDelete('set null');
        });
    }
};
