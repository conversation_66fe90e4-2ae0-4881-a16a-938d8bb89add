<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class IncidentRequest extends FormRequest
{
    public function authorize()
    {
        return true;
    }

    public function rules()
    {
        return [
            'car_plate' => 'required|string',
            'driver_id' => 'required|exists:drivers,id',
            'incident_description' => 'required|string',
            'date_time' => 'required|date',
            'images.*' => 'nullable|image|mimes:jpeg,png,jpg|max:2048',
            'supervisor_approval' => 'boolean'
        ];
    }

    protected function prepareForValidation()
    {
        $this->merge([
            'supervisor_approval' => filter_var($this->supervisor_approval, FILTER_VALIDATE_BOOLEAN),
            'date_time' => $this->date_time ?? now()
        ]);
    }
}